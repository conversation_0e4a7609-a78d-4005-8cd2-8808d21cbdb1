<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Tenant;
use App\Models\Drug;
use App\Models\Customer;
use App\Models\Batch;
use App\Models\Invoice;
use App\Services\InvoiceService;
use Carbon\Carbon;

class SetupInvoiceDemo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invoice:demo {--tenant=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup demo data for invoice system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tenantId = $this->option('tenant') ?? '6077bd52-14ed-48fc-a13d-962c9060795f';

        $tenant = Tenant::find($tenantId);
        if (!$tenant) {
            $this->error("Tenant {$tenantId} not found");
            return 1;
        }

        $this->info("Setting up demo data for tenant: {$tenant->id}");

        $tenant->run(function () {
            $this->createDemoData();
        });

        $this->info("Demo data setup completed!");
        return 0;
    }

    private function createDemoData()
    {
        // Create some demo drugs if they don't exist
        $drugs = $this->createDemoDrugs();

        // Create demo customers
        $customers = $this->createDemoCustomers();

        // Create demo batches
        $this->createDemoBatches($drugs);

        // Create demo invoices
        $this->createDemoInvoices($customers);
    }

    private function createDemoDrugs()
    {
        $this->info('Creating demo drugs...');

        $drugsData = [
            [
                'code' => 'PARA500',
                'name' => 'Paracetamol 500mg',
                'generic_name' => 'Paracetamol',
                'active_ingredient' => 'Paracetamol',
                'concentration' => '500mg',
                'dosage_form' => 'Viên nén',
                'unit' => 'Viên',
                'manufacturer' => 'Công ty Dược ABC',
                'country_of_origin' => 'Việt Nam',
                'prescription_required' => false,
                'controlled_substance' => false,
                'storage_condition' => 'Nơi khô ráo, thoáng mát',
                'therapeutic_class' => 'Thuốc giảm đau, hạ sốt',
                'price' => 2000,
                'wholesale_price' => 1500,
                'is_active' => true,
                'description' => 'Thuốc giảm đau, hạ sốt',
            ],
            [
                'code' => 'AMOX250',
                'name' => 'Amoxicillin 250mg',
                'generic_name' => 'Amoxicillin',
                'active_ingredient' => 'Amoxicillin',
                'concentration' => '250mg',
                'dosage_form' => 'Viên nang',
                'unit' => 'Viên',
                'manufacturer' => 'Công ty Dược XYZ',
                'country_of_origin' => 'Việt Nam',
                'prescription_required' => true,
                'controlled_substance' => false,
                'storage_condition' => 'Nơi khô ráo, thoáng mát',
                'therapeutic_class' => 'Kháng sinh',
                'price' => 5000,
                'wholesale_price' => 3500,
                'is_active' => true,
                'description' => 'Kháng sinh điều trị nhiễm khuẩn',
            ],
        ];

        $drugs = collect();
        foreach ($drugsData as $drugData) {
            $drug = Drug::firstOrCreate(['code' => $drugData['code']], $drugData);
            $drugs->push($drug);
        }

        return $drugs;
    }

    private function createDemoCustomers()
    {
        $this->info('Creating demo customers...');

        $customersData = [
            [
                'code' => 'KH001',
                'type' => 'individual',
                'name' => 'Nguyễn Văn A',
                'phone' => '0901234567',
                'email' => '<EMAIL>',
                'date_of_birth' => '1980-01-01',
                'gender' => 'male',
                'address' => '123 Đường ABC, Quận 1',
                'customer_group' => 'regular',
                'loyalty_points' => 100,
                'total_spent' => 500000,
                'status' => 'active',
            ],
            [
                'code' => 'KH002',
                'type' => 'individual',
                'name' => 'Trần Thị B',
                'phone' => '0987654321',
                'email' => '<EMAIL>',
                'date_of_birth' => '1985-05-15',
                'gender' => 'female',
                'address' => '456 Đường XYZ, Quận 2',
                'customer_group' => 'vip',
                'loyalty_points' => 500,
                'total_spent' => 2000000,
                'status' => 'active',
            ],
        ];

        $customers = collect();
        foreach ($customersData as $customerData) {
            $customer = Customer::firstOrCreate(['code' => $customerData['code']], $customerData);
            $customers->push($customer);
        }

        return $customers;
    }

    private function createDemoBatches($drugs)
    {
        $this->info('Creating demo batches...');

        foreach ($drugs as $drug) {
            // Tạo 2-3 lô cho mỗi thuốc
            $batchCount = rand(2, 3);

            for ($i = 1; $i <= $batchCount; $i++) {
                $manufactureDate = Carbon::now()->subMonths(rand(1, 6));
                $expiryDate = $manufactureDate->copy()->addYears(rand(2, 5));
                $importDate = $manufactureDate->copy()->addDays(rand(1, 30));

                Batch::firstOrCreate([
                    'drug_id' => $drug->id,
                    'batch_number' => 'LOT' . str_pad($drug->id, 3, '0', STR_PAD_LEFT) . str_pad($i, 2, '0', STR_PAD_LEFT),
                ], [
                    'manufacture_date' => $manufactureDate,
                    'expiry_date' => $expiryDate,
                    'import_date' => $importDate,
                    'import_price' => $drug->wholesale_price,
                    'selling_price' => $drug->price,
                    'initial_quantity' => rand(100, 500),
                    'current_quantity' => rand(50, 200),
                    'reserved_quantity' => 0,
                    'import_invoice_number' => 'IMP' . date('Ymd') . str_pad($drug->id * $i, 4, '0', STR_PAD_LEFT),
                    'notes' => 'Lô demo từ command',
                    'is_active' => true,
                ]);
            }
        }
    }

    private function createDemoInvoices($customers)
    {
        $this->info('Creating demo invoices...');

        $invoiceService = app(InvoiceService::class);
        $batches = Batch::with('drug')->get();

        if ($batches->isEmpty()) {
            $this->warn('No batches found, skipping invoice creation');
            return;
        }

        // Tạo 5 hóa đơn demo
        for ($i = 1; $i <= 5; $i++) {
            $customer = $customers->random();
            $selectedBatches = $batches->random(rand(1, 3));

            $items = [];
            foreach ($selectedBatches as $batch) {
                $quantity = rand(1, min(5, $batch->current_quantity));
                if ($quantity > 0) {
                    $items[] = [
                        'batch_id' => $batch->id,
                        'quantity' => $quantity,
                        'unit_price' => $batch->selling_price,
                        'discount_percentage' => rand(0, 10),
                    ];
                }
            }

            if (!empty($items)) {
                try {
                    $invoice = $invoiceService->createInvoice([
                        'customer_id' => $customer->id,
                        'invoice_date' => Carbon::now()->subDays(rand(0, 30)),
                        'payment_method' => collect(['cash', 'card', 'transfer'])->random(),
                        'payment_status' => 'paid',
                        'items' => $items,
                        'notes' => "Hóa đơn demo #{$i}",
                        'auto_confirm' => true,
                    ]);

                    $this->info("Created invoice: {$invoice->invoice_number}");
                } catch (\Exception $e) {
                    $this->warn("Failed to create invoice #{$i}: " . $e->getMessage());
                }
            }
        }
    }
}
