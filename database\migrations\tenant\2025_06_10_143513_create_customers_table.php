<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->enum('type', ['individual', 'company'])->default('individual');
            $table->string('name');
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->date('date_of_birth')->nullable();
            $table->enum('gender', ['male', 'female', 'other'])->nullable();
            $table->string('id_number')->nullable();
            $table->text('address')->nullable();
            $table->string('ward')->nullable();
            $table->string('district')->nullable();
            $table->string('province')->nullable();
            $table->enum('customer_group', ['retail', 'wholesale', 'vip'])->default('retail');
            $table->string('tax_code')->nullable();
            $table->string('company_name')->nullable();
            $table->integer('loyalty_points')->default(0);
            $table->decimal('total_spent', 15, 2)->default(0);
            $table->timestamp('last_visit')->nullable();
            $table->enum('status', ['active', 'inactive', 'blocked'])->default('active');
            $table->text('notes')->nullable();
            $table->string('avatar_url')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['name', 'status']);
            $table->index(['phone']);
            $table->index(['email']);
            $table->index(['customer_group']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
