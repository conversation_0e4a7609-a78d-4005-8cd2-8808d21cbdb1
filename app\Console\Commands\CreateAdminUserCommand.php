<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateAdminUserCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenant:create-admin
                            {tenant_id : The tenant ID}
                            {--name= : Admin name}
                            {--email= : Admin email}
                            {--password= : Admin password}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create admin user for a specific tenant';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tenantId = $this->argument('tenant_id');

        // Find tenant
        $tenant = \App\Models\Tenant::find($tenantId);
        if (!$tenant) {
            $this->error("Tenant with ID {$tenantId} not found");
            return 1;
        }

        // Get admin information
        $name = $this->option('name') ?: $this->ask('Admin name', 'Admin');
        $email = $this->option('email') ?: $this->ask('Admin email', 'admin@' . $tenant->subdomain . '.com');
        $password = $this->option('password') ?: $this->secret('Admin password');

        if (!$password) {
            $password = 'password';
            $this->warn('Using default password: password');
        }

        // Create admin user in tenant context
        $tenant->run(function () use ($name, $email, $password) {
            $user = User::create([
                'name' => $name,
                'email' => $email,
                'password' => Hash::make($password),
                'role' => 'owner',
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            $this->info('Admin user created successfully!');
            $this->table(['Field', 'Value'], [
                ['Name', $user->name],
                ['Email', $user->email],
                ['Role', $user->role],
                ['Active', $user->is_active ? 'Yes' : 'No'],
            ]);
        });

        return 0;
    }
}
