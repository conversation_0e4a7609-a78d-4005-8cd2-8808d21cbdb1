<?php

namespace App\Filament\SuperAdmin\Resources;

use App\Filament\SuperAdmin\Resources\TenantResource\Pages;
use App\Filament\SuperAdmin\Resources\TenantResource\RelationManagers;
use App\Models\Tenant;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TenantResource extends Resource
{
    protected static ?string $model = Tenant::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';
    protected static ?string $navigationLabel = 'Tenants';
    protected static ?string $modelLabel = 'Tenant';
    protected static ?string $pluralModelLabel = 'Tenants';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Tên nhà thuốc')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('owner_name')
                    ->label('Tên chủ sở hữu')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('owner_email')
                    ->label('Email chủ sở hữu')
                    ->email()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('owner_phone')
                    ->label('Điện thoại')
                    ->tel()
                    ->maxLength(20),
                Forms\Components\Select::make('subscription_plan')
                    ->label('Gói dịch vụ')
                    ->options([
                        'trial' => 'Trial',
                        'basic' => 'Basic',
                        'premium' => 'Premium',
                        'enterprise' => 'Enterprise',
                    ])
                    ->required(),
                Forms\Components\Select::make('subscription_status')
                    ->label('Trạng thái')
                    ->options([
                        'trial' => 'Trial',
                        'active' => 'Active',
                        'suspended' => 'Suspended',
                        'cancelled' => 'Cancelled',
                    ])
                    ->required(),
                Forms\Components\DateTimePicker::make('trial_ends_at')
                    ->label('Trial kết thúc'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên nhà thuốc')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('owner_name')
                    ->label('Chủ sở hữu')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('owner_email')
                    ->label('Email')
                    ->searchable(),
                Tables\Columns\BadgeColumn::make('subscription_plan')
                    ->label('Gói')
                    ->colors([
                        'warning' => 'trial',
                        'success' => 'basic',
                        'primary' => 'premium',
                        'danger' => 'enterprise',
                    ]),
                Tables\Columns\BadgeColumn::make('subscription_status')
                    ->label('Trạng thái')
                    ->colors([
                        'warning' => 'trial',
                        'success' => 'active',
                        'danger' => 'suspended',
                        'secondary' => 'cancelled',
                    ]),
                Tables\Columns\TextColumn::make('trial_ends_at')
                    ->label('Trial kết thúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTenants::route('/'),
            'create' => Pages\CreateTenant::route('/create'),
            'edit' => Pages\EditTenant::route('/{record}/edit'),
        ];
    }
}
