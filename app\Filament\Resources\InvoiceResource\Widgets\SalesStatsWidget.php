<?php

namespace App\Filament\Resources\InvoiceResource\Widgets;

use App\Models\Invoice;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class SalesStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $todayInvoices = Invoice::confirmed()->today()->count();
        $todayRevenue = Invoice::confirmed()->today()->sum('total_amount');
        $monthlyRevenue = Invoice::confirmed()->thisMonth()->sum('total_amount');
        $pendingInvoices = Invoice::where('status', 'draft')->count();

        return [
            Stat::make('Hóa đơn hôm nay', $todayInvoices)
                ->description('Số hóa đơn đã xác nhận')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('success'),

            Stat::make('<PERSON>anh thu hôm nay', number_format($todayRevenue) . ' ₫')
                ->description('Tổng doanh thu trong ngày')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success'),

            Stat::make('Doanh thu tháng', number_format($monthlyRevenue) . ' ₫')
                ->description('Tổng doanh thu trong tháng')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('primary'),

            Stat::make('Hóa đơn chờ xử lý', $pendingInvoices)
                ->description('Hóa đơn nháp cần xác nhận')
                ->descriptionIcon('heroicon-m-clock')
                ->color($pendingInvoices > 0 ? 'warning' : 'success'),
        ];
    }
}
