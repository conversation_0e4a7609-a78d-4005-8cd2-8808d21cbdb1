<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Batch extends Model
{
    use HasFactory;

    protected $fillable = [
        'drug_id', 'batch_number', 'manufacture_date', 'expiry_date',
        'import_date', 'import_price', 'selling_price', 'initial_quantity',
        'current_quantity', 'reserved_quantity', 'supplier_id',
        'import_invoice_number', 'notes', 'is_active'
    ];

    protected $casts = [
        'manufacture_date' => 'date',
        'expiry_date' => 'date',
        'import_date' => 'date',
        'import_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'initial_quantity' => 'integer',
        'current_quantity' => 'integer',
        'reserved_quantity' => 'integer',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function drug(): BelongsTo
    {
        return $this->belongsTo(Drug::class);
    }

    public function invoiceItems(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    // Scopes
    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->where('expiry_date', '<=', now()->addDays($days))
            ->where('current_quantity', '>', 0);
    }

    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now());
    }

    public function scopeAvailable($query)
    {
        return $query->where('current_quantity', '>', 0)
            ->where('expiry_date', '>', now())
            ->where('is_active', true);
    }

    public function scopeFefo($query)
    {
        return $query->available()
            ->orderBy('expiry_date', 'asc')
            ->orderBy('created_at', 'asc');
    }

    // Accessors
    public function getIsExpiredAttribute(): bool
    {
        return $this->expiry_date->isPast();
    }

    public function getIsExpiringSoonAttribute(): bool
    {
        return $this->expiry_date->diffInDays() <= 30;
    }

    public function getAvailableQuantityAttribute(): int
    {
        return $this->current_quantity - $this->reserved_quantity;
    }

    public function getDaysToExpiryAttribute(): int
    {
        return now()->diffInDays($this->expiry_date, false);
    }

    // Business Methods
    public function canSell(int $quantity): bool
    {
        return $this->available_quantity >= $quantity && !$this->is_expired;
    }

    public function sell(int $quantity): bool
    {
        if (!$this->canSell($quantity)) {
            return false;
        }

        $this->decrement('current_quantity', $quantity);
        return true;
    }

    public function reserve(int $quantity): bool
    {
        if ($this->available_quantity < $quantity) {
            return false;
        }

        $this->increment('reserved_quantity', $quantity);
        return true;
    }

    public function unreserve(int $quantity): bool
    {
        if ($this->reserved_quantity < $quantity) {
            return false;
        }

        $this->decrement('reserved_quantity', $quantity);
        return true;
    }

    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        if ($this->is_expired) {
            return 'expired';
        }

        if ($this->is_expiring_soon) {
            return 'expiring_soon';
        }

        if ($this->current_quantity <= 0) {
            return 'out_of_stock';
        }

        if ($this->available_quantity <= 0) {
            return 'reserved';
        }

        return 'available';
    }
}
