<?php

namespace App\Filament\Resources\InvoiceResource\RelationManagers;

use App\Models\Batch;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'items';

    protected static ?string $title = 'Chi tiết hóa đơn';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('batch_id')
                    ->label('Lô thuốc')
                    ->relationship('batch', 'batch_number')
                    ->getOptionLabelFromRecordUsing(fn (Batch $record) =>
                        "{$record->drug->name} - Lô: {$record->batch_number} (Tồn: {$record->current_quantity}) - " . number_format($record->selling_price) . "₫"
                    )
                    ->searchable(['batch_number'])
                    ->preload()
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state) {
                            $batch = Batch::with('drug')->find($state);
                            $set('unit_price', $batch->selling_price);
                            $set('tax_rate', $batch->drug->tax_rate * 100);
                        }
                    }),

                TextInput::make('quantity')
                    ->label('Số lượng')
                    ->numeric()
                    ->required()
                    ->minValue(1),

                TextInput::make('unit_price')
                    ->label('Đơn giá')
                    ->numeric()
                    ->required()
                    ->prefix('₫'),

                TextInput::make('discount_percentage')
                    ->label('Giảm giá (%)')
                    ->numeric()
                    ->suffix('%')
                    ->minValue(0)
                    ->maxValue(100)
                    ->default(0),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('batch.drug.name')
            ->columns([
                TextColumn::make('batch.drug.name')
                    ->label('Tên thuốc')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('batch.batch_number')
                    ->label('Số lô')
                    ->searchable(),

                TextColumn::make('quantity')
                    ->label('Số lượng')
                    ->sortable(),

                TextColumn::make('unit_price')
                    ->label('Đơn giá')
                    ->money('VND')
                    ->sortable(),

                TextColumn::make('discount_percentage')
                    ->label('Giảm giá')
                    ->suffix('%'),

                TextColumn::make('tax_rate')
                    ->label('Thuế')
                    ->suffix('%'),

                TextColumn::make('total_price')
                    ->label('Thành tiền')
                    ->money('VND')
                    ->sortable()
                    ->weight('bold'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Thêm sản phẩm'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
