<?php

namespace App\Filament\Resources\InvoiceResource\Pages;

use App\Filament\Resources\InvoiceResource;
use App\Services\InvoiceService;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateInvoice extends CreateRecord
{
    protected static string $resource = InvoiceResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Hóa đơn đã được tạo thành công';
    }

    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model
    {
        try {
            $invoiceService = app(InvoiceService::class);
            $invoice = $invoiceService->createInvoice($data);

            Notification::make()
                ->title('Thành công!')
                ->body("Hóa đơn #{$invoice->invoice_number} đã được tạo")
                ->success()
                ->send();

            return $invoice;
        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi!')
                ->body('Không thể tạo hóa đơn: ' . $e->getMessage())
                ->danger()
                ->send();

            throw $e;
        }
    }
}
