<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Drug extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'code', 'name', 'generic_name', 'active_ingredient',
        'concentration', 'dosage_form', 'unit', 'manufacturer',
        'country_of_origin', 'prescription_required', 'controlled_substance',
        'storage_condition', 'therapeutic_class', 'price', 'wholesale_price',
        'is_active', 'description', 'side_effects', 'contraindications'
    ];

    protected $casts = [
        'prescription_required' => 'boolean',
        'controlled_substance' => 'boolean',
        'is_active' => 'boolean',
        'price' => 'decimal:2',
        'wholesale_price' => 'decimal:2',
        'side_effects' => 'array',
        'contraindications' => 'array',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePrescriptionRequired($query)
    {
        return $query->where('prescription_required', true);
    }

    public function scopeByDosageForm($query, $form)
    {
        return $query->where('dosage_form', $form);
    }

    // Accessors
    public function getFormattedPriceAttribute()
    {
        return number_format($this->price, 0, ',', '.') . ' ₫';
    }
}
