<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Drug extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'code', 'name', 'generic_name', 'active_ingredient',
        'concentration', 'dosage_form', 'unit', 'manufacturer',
        'country_of_origin', 'prescription_required', 'controlled_substance',
        'storage_condition', 'therapeutic_class', 'price', 'wholesale_price',
        'is_active', 'description', 'side_effects', 'contraindications'
    ];

    protected $casts = [
        'prescription_required' => 'boolean',
        'controlled_substance' => 'boolean',
        'is_active' => 'boolean',
        'price' => 'decimal:2',
        'wholesale_price' => 'decimal:2',
        'side_effects' => 'array',
        'contraindications' => 'array',
    ];

    // Relationships
    public function batches()
    {
        return $this->hasMany(Batch::class);
    }

    public function availableBatches()
    {
        return $this->hasMany(Batch::class)->available();
    }

    public function invoiceItems()
    {
        return $this->hasManyThrough(InvoiceItem::class, Batch::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePrescriptionRequired($query)
    {
        return $query->where('prescription_required', true);
    }

    public function scopeControlledSubstance($query)
    {
        return $query->where('controlled_substance', true);
    }

    public function scopeLowStock($query, $threshold = 10)
    {
        return $query->whereHas('batches', function ($q) use ($threshold) {
            $q->available();
        })->havingRaw('SUM(batches.current_quantity) <= ?', [$threshold]);
    }

    // Accessors
    public function getTotalStockAttribute(): int
    {
        return $this->batches()->available()->sum('current_quantity');
    }

    public function getTaxRateAttribute(): float
    {
        return $this->prescription_required ? 0.05 : 0.10; // 5% for prescription, 10% for OTC
    }

    public function getEarliestExpiryAttribute()
    {
        return $this->batches()->available()->min('expiry_date');
    }

    // Business Methods
    public function hasStock(int $quantity = 1): bool
    {
        return $this->total_stock >= $quantity;
    }

    public function getAvailableBatchesForSale(int $quantity): \Illuminate\Database\Eloquent\Collection
    {
        $batches = collect();
        $remainingQuantity = $quantity;

        $availableBatches = $this->batches()->fefo()->get();

        foreach ($availableBatches as $batch) {
            if ($remainingQuantity <= 0) {
                break;
            }

            $availableInBatch = min($batch->available_quantity, $remainingQuantity);
            if ($availableInBatch > 0) {
                $batch->selected_quantity = $availableInBatch;
                $batches->push($batch);
                $remainingQuantity -= $availableInBatch;
            }
        }

        return $batches;
    }

    public function scopeByDosageForm($query, $form)
    {
        return $query->where('dosage_form', $form);
    }

    // Accessors
    public function getFormattedPriceAttribute()
    {
        return number_format($this->price, 0, ',', '.') . ' ₫';
    }
}
