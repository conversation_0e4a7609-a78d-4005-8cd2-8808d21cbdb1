<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('batches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('drug_id')->constrained()->onDelete('cascade');
            $table->string('batch_number', 50)->comment('Số lô');
            $table->date('manufacture_date')->nullable()->comment('Ngày sản xuất');
            $table->date('expiry_date')->comment('Hạn sử dụng');
            $table->date('import_date')->comment('Ngày nhập kho');
            $table->decimal('import_price', 15, 2)->comment('Giá nhập');
            $table->decimal('selling_price', 15, 2)->comment('<PERSON><PERSON><PERSON> bán');
            $table->integer('initial_quantity')->comment('S<PERSON> lượng nhập ban đầu');
            $table->integer('current_quantity')->comment('Số lượng hiện tại');
            $table->integer('reserved_quantity')->default(0)->comment('Số lượng đã đặt');
            $table->foreignId('supplier_id')->nullable()->comment('Nhà cung cấp');
            $table->string('import_invoice_number', 50)->nullable()->comment('Số hóa đơn nhập');
            $table->text('notes')->nullable()->comment('Ghi chú');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['drug_id']);
            $table->index(['expiry_date']);
            $table->index(['current_quantity']);
            $table->index(['batch_number']);
            $table->unique(['drug_id', 'batch_number'], 'uk_batches_drug_batch');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('batches');
    }
};
