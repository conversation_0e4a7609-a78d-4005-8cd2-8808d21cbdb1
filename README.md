# 🏥 Pharmacy Management System (PMQL-HieuThuoc)

Hệ thống quản lý nhà thuốc đa tenant v<PERSON><PERSON> 11 + Filament 3 + Multi-tenancy

## 📋 Tổng quan

Đây là hệ thống SaaS quản lý nhà thuốc với kiến trúc multi-tenant, cho phép nhiều nhà thuốc sử dụng chung một hệ thống với dữ liệu riêng biệt.

### 🏗️ Kiến trúc hệ thống:
- **Backend:** Laravel 11 + Filament 3 Admin Panel
- **Database:** MySQL với database per tenant strategy
- **Multi-tenancy:** Stancl/Tenancy package
- **Frontend:** Blade + Livewire + Tailwind CSS
- **Mobile:** Flutter (sẽ phát triển)

## 🚀 Cài đặt và Chạy

### Yêu cầu:
- PHP 8.2+
- MySQL 8.0+
- Composer
- <PERSON><PERSON> (hoặc XAMPP/WAMP)

### <PERSON><PERSON><PERSON> bước cài đặt:

1. **Clone repository:**
```bash
<NAME_EMAIL>:Webest-Group/pmql-hieuthuoc.git
cd pmql-hieuthuoc
```

2. **Cài đặt dependencies:**
```bash
composer install
```

3. **Cấu hình environment:**
```bash
cp .env.example .env
php artisan key:generate
```

4. **Cấu hình database trong .env:**
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=pharmasaas_central
DB_USERNAME=root
DB_PASSWORD=
```

5. **Chạy migrations:**
```bash
php artisan migrate
```

6. **Publish Filament assets:**
```bash
php artisan filament:assets
```

## 🏪 Demo Tenants

Hệ thống đã có sẵn 2 nhà thuốc demo để test:

### 🏥 **XYZ Pharmacy**
- **Tenant ID:** `6077bd52-14ed-48fc-a13d-962c9060795f`
- **Tên nhà thuốc:** XYZ Pharmacy
- **Chủ sở hữu:** Tran Thi B
- **Email:** <EMAIL>
- **Điện thoại:** 0987654321
- **Gói:** Trial (kết thúc: 24/06/2025)

**🔐 Tài khoản admin:**
- **Email:** `<EMAIL>`
- **Password:** `password123`

### 🏥 **ABC Pharmacy**
- **Tenant ID:** `2baa69c9-3c10-47f3-a4bb-e845782cdb1f`
- **Tên nhà thuốc:** ABC Pharmacy
- **Chủ sở hữu:** Nguyen Van A
- **Email:** <EMAIL>
- **Điện thoại:** 0901234567
- **Gói:** Trial (kết thúc: 24/06/2025)

**🔐 Tài khoản admin:**
- **Email:** `<EMAIL>`
- **Password:** `password123`

## 🌐 URLs Demo

### 📊 **Trang chủ & Danh sách tenants:**
```
http://pmql-hieuthuoc.test/tenants
```

### 🏥 **XYZ Pharmacy:**
- **Dashboard:** `http://pmql-hieuthuoc.test/tenant/6077bd52-14ed-48fc-a13d-962c9060795f`
- **Admin Panel:** `http://pmql-hieuthuoc.test/tenant/6077bd52-14ed-48fc-a13d-962c9060795f/admin`

### 🏥 **ABC Pharmacy:**
- **Dashboard:** `http://pmql-hieuthuoc.test/tenant/2baa69c9-3c10-47f3-a4bb-e845782cdb1f`
- **Admin Panel:** `http://pmql-hieuthuoc.test/tenant/2baa69c9-3c10-47f3-a4bb-e845782cdb1f/admin`

## 🛠️ Quản lý Tenants

### Tạo tenant mới:
```bash
php artisan tenant:create {subdomain} --pharmacy="Tên nhà thuốc" --owner="Tên chủ" --email="<EMAIL>" --phone="0123456789"
```

### Tạo admin user cho tenant:
```bash
php artisan tenant:create-admin {tenant_id} --name="Admin Name" --email="<EMAIL>" --password="password123"
```

### Ví dụ tạo tenant mới:
```bash
# Tạo tenant
php artisan tenant:create demo-pharmacy --pharmacy="Demo Pharmacy" --owner="John Doe" --email="<EMAIL>" --phone="0123456789"

# Tạo admin user (thay {tenant_id} bằng ID thực tế)
php artisan tenant:create-admin {tenant_id} --name="Admin Demo" --email="<EMAIL>" --password="password123"
```

## 📱 Tính năng hiện tại

### ✅ **Đã hoàn thành:**
- **TenantAgent:** Multi-tenancy với database per tenant
- **FilamentAgent:** Admin panel với Filament 3
- **Drug Management:** Quản lý thuốc với form đầy đủ
- **Customer Management:** Quản lý khách hàng
- **Dashboard Widgets:** Thống kê cơ bản
- **Authentication:** Đăng nhập/đăng xuất
- **Path-based Routing:** Truy cập qua URL path

### 🔄 **Đang phát triển:**
- **InvoiceAgent:** Hệ thống hóa đơn và bán hàng
- **AuthAgent:** Roles & permissions
- **ReportAgent:** Báo cáo và thống kê
- **MobileAgent:** API cho Flutter app

## 🎯 Roadmap

### Phase 1 (Hiện tại):
- [x] Multi-tenancy setup
- [x] Basic admin panel
- [x] Drug & Customer management

### Phase 2:
- [ ] Invoice & Sales system
- [ ] Inventory management
- [ ] Reports & Analytics

### Phase 3:
- [ ] Mobile app (Flutter)
- [ ] Advanced features
- [ ] Payment integration

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📞 Liên hệ

- **Repository:** [pmql-hieuthuoc](https://github.com/Webest-Group/pmql-hieuthuoc)
- **Domain local:** `pmql-hieuthuoc.test`

---

**🎉 Happy Coding!** 🚀
