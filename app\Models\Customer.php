<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'code', 'type', 'name', 'phone', 'email', 'date_of_birth',
        'gender', 'id_number', 'address', 'ward', 'district', 'province',
        'customer_group', 'tax_code', 'company_name', 'loyalty_points',
        'total_spent', 'last_visit', 'status', 'notes', 'avatar_url'
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'last_visit' => 'datetime',
        'loyalty_points' => 'integer',
        'total_spent' => 'decimal:2',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByGroup($query, $group)
    {
        return $query->where('customer_group', $group);
    }

    // Accessors
    public function getFullAddressAttribute()
    {
        $parts = array_filter([$this->address, $this->ward, $this->district, $this->province]);
        return implode(', ', $parts);
    }

    public function getFormattedTotalSpentAttribute()
    {
        return number_format($this->total_spent, 0, ',', '.') . ' ₫';
    }
}
