<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'code', 'type', 'name', 'phone', 'email', 'date_of_birth',
        'gender', 'id_number', 'address', 'ward', 'district', 'province',
        'customer_group', 'tax_code', 'company_name', 'loyalty_points',
        'total_spent', 'last_visit', 'status', 'notes', 'avatar_url'
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'last_visit' => 'datetime',
        'loyalty_points' => 'integer',
        'total_spent' => 'decimal:2',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByGroup($query, $group)
    {
        return $query->where('customer_group', $group);
    }

    // Relationships
    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    // Business Methods
    public function updatePurchaseStats(float $amount): void
    {
        $this->increment('total_spent', $amount);
        $this->update(['last_visit' => now()]);
    }

    public function earnPoints(float $amount): void
    {
        // 1 point per 1000 VND spent
        $points = (int) floor($amount / 1000);
        $this->increment('loyalty_points', $points);
    }

    public function calculateDiscount(float $amount): float
    {
        $discountRate = 0;

        // VIP customers get 5% discount
        if ($this->customer_group === 'vip') {
            $discountRate = 0.05;
        }
        // Regular customers with high loyalty points get 2% discount
        elseif ($this->loyalty_points >= 1000) {
            $discountRate = 0.02;
        }

        return $amount * $discountRate;
    }

    public function getDiscountRateAttribute(): float
    {
        if ($this->customer_group === 'vip') {
            return 5.0; // 5%
        } elseif ($this->loyalty_points >= 1000) {
            return 2.0; // 2%
        }

        return 0.0;
    }

    // Accessors
    public function getFullAddressAttribute()
    {
        $parts = array_filter([$this->address, $this->ward, $this->district, $this->province]);
        return implode(', ', $parts);
    }

    public function getFormattedTotalSpentAttribute()
    {
        return number_format($this->total_spent, 0, ',', '.') . ' ₫';
    }
}
