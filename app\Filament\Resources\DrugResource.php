<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DrugResource\Pages;
use App\Filament\Resources\DrugResource\RelationManagers;
use App\Models\Drug;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DrugResource extends Resource
{
    protected static ?string $model = Drug::class;

    protected static ?string $navigationIcon = 'heroicon-o-beaker';

    protected static ?string $navigationLabel = 'Thuốc';

    protected static ?string $modelLabel = 'Thuốc';

    protected static ?string $pluralModelLabel = 'Thuốc';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Thông tin cơ bản')
                    ->schema([
                        TextInput::make('code')
                            ->label('Mã thuốc')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(50),

                        TextInput::make('name')
                            ->label('Tên thuốc')
                            ->required()
                            ->maxLength(255),

                        TextInput::make('generic_name')
                            ->label('Tên hoạt chất')
                            ->maxLength(255),

                        Textarea::make('active_ingredient')
                            ->label('Thành phần hoạt chất')
                            ->rows(3),

                        TextInput::make('concentration')
                            ->label('Nồng độ')
                            ->maxLength(100),

                        Select::make('dosage_form')
                            ->label('Dạng bào chế')
                            ->options([
                                'tablet' => 'Viên nén',
                                'capsule' => 'Viên nang',
                                'syrup' => 'Siro',
                                'injection' => 'Tiêm',
                                'cream' => 'Kem',
                                'ointment' => 'Thuốc mỡ',
                                'drops' => 'Thuốc nhỏ',
                                'powder' => 'Bột',
                                'solution' => 'Dung dịch',
                            ]),

                        TextInput::make('unit')
                            ->label('Đơn vị tính')
                            ->required()
                            ->maxLength(50),
                    ])
                    ->columns(2),

                Section::make('Thông tin nhà sản xuất')
                    ->schema([
                        TextInput::make('manufacturer')
                            ->label('Nhà sản xuất')
                            ->maxLength(255),

                        TextInput::make('country_of_origin')
                            ->label('Nước sản xuất')
                            ->maxLength(100),

                        TextInput::make('storage_condition')
                            ->label('Điều kiện bảo quản')
                            ->maxLength(255),

                        TextInput::make('therapeutic_class')
                            ->label('Nhóm điều trị')
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Section::make('Giá và trạng thái')
                    ->schema([
                        TextInput::make('price')
                            ->label('Giá bán')
                            ->required()
                            ->numeric()
                            ->prefix('₫'),

                        TextInput::make('wholesale_price')
                            ->label('Giá sỉ')
                            ->numeric()
                            ->prefix('₫'),

                        Toggle::make('prescription_required')
                            ->label('Cần đơn thuốc'),

                        Toggle::make('controlled_substance')
                            ->label('Chất kiểm soát'),

                        Toggle::make('is_active')
                            ->label('Đang hoạt động')
                            ->default(true),
                    ])
                    ->columns(3),

                Section::make('Mô tả và ghi chú')
                    ->schema([
                        Textarea::make('description')
                            ->label('Mô tả')
                            ->rows(3),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->label('Mã thuốc')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('name')
                    ->label('Tên thuốc')
                    ->searchable()
                    ->sortable()
                    ->limit(50),

                TextColumn::make('generic_name')
                    ->label('Hoạt chất')
                    ->searchable()
                    ->limit(30)
                    ->toggleable(),

                TextColumn::make('dosage_form')
                    ->label('Dạng bào chế')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'tablet' => 'success',
                        'capsule' => 'info',
                        'syrup' => 'warning',
                        'injection' => 'danger',
                        default => 'gray',
                    }),

                TextColumn::make('unit')
                    ->label('Đơn vị'),

                TextColumn::make('price')
                    ->label('Giá bán')
                    ->money('VND')
                    ->sortable(),

                IconColumn::make('prescription_required')
                    ->label('Đơn thuốc')
                    ->boolean()
                    ->toggleable(),

                IconColumn::make('is_active')
                    ->label('Hoạt động')
                    ->boolean(),
            ])
            ->filters([
                SelectFilter::make('dosage_form')
                    ->label('Dạng bào chế')
                    ->options([
                        'tablet' => 'Viên nén',
                        'capsule' => 'Viên nang',
                        'syrup' => 'Siro',
                        'injection' => 'Tiêm',
                        'cream' => 'Kem',
                        'ointment' => 'Thuốc mỡ',
                        'drops' => 'Thuốc nhỏ',
                        'powder' => 'Bột',
                        'solution' => 'Dung dịch',
                    ]),

                TernaryFilter::make('prescription_required')
                    ->label('Cần đơn thuốc'),

                TernaryFilter::make('is_active')
                    ->label('Đang hoạt động'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDrugs::route('/'),
            'create' => Pages\CreateDrug::route('/create'),
            'edit' => Pages\EditDrug::route('/{record}/edit'),
        ];
    }
}
