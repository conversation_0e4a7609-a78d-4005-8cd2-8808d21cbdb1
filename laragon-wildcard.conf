# Apache Virtual Host for Wildcard Subdomain
# Copy this to: C:\laragon\etc\apache2\sites-enabled\pmql-hieuthuoc.conf

<VirtualHost *:80>
    DocumentRoot "D:/laragon/www/pmql-hieuthuoc/public"
    ServerName pmql-hieuthuoc.test
    ServerAlias *.pmql-hieuthuoc.test
    
    <Directory "D:/laragon/www/pmql-hieuthuoc/public">
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog "D:/laragon/www/pmql-hieuthuoc/storage/logs/apache-error.log"
    CustomLog "D:/laragon/www/pmql-hieuthuoc/storage/logs/apache-access.log" combined
</VirtualHost>

# Nếu dùng HTTPS
<VirtualHost *:443>
    DocumentRoot "D:/laragon/www/pmql-hieuthuoc/public"
    ServerName pmql-hieuthuoc.test
    ServerAlias *.pmql-hieuthuoc.test
    
    SSLEngine on
    SSLCertificateFile "C:/laragon/etc/ssl/laragon.crt"
    SSLCertificateKeyFile "C:/laragon/etc/ssl/laragon.key"
    
    <Directory "D:/laragon/www/pmql-hieuthuoc/public">
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog "D:/laragon/www/pmql-hieuthuoc/storage/logs/apache-error.log"
    CustomLog "D:/laragon/www/pmql-hieuthuoc/storage/logs/apache-access.log" combined
</VirtualHost>
