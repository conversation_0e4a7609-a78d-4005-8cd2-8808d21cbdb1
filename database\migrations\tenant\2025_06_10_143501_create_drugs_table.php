<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('drugs', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('name');
            $table->string('generic_name')->nullable();
            $table->text('active_ingredient')->nullable();
            $table->string('concentration')->nullable();
            $table->string('dosage_form')->nullable();
            $table->string('unit');
            $table->string('manufacturer')->nullable();
            $table->string('country_of_origin')->nullable();
            $table->boolean('prescription_required')->default(false);
            $table->boolean('controlled_substance')->default(false);
            $table->string('storage_condition')->nullable();
            $table->string('therapeutic_class')->nullable();
            $table->decimal('price', 15, 2);
            $table->decimal('wholesale_price', 15, 2)->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('description')->nullable();
            $table->json('side_effects')->nullable();
            $table->json('contraindications')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['name', 'is_active']);
            $table->index(['code']);
            $table->fullText(['name', 'generic_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('drugs');
    }
};
