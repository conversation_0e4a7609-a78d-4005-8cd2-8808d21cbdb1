<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect('/tenants');
});

// Redirect admin access to tenants list
Route::get('/admin', function () {
    return redirect('/tenants')->with('message', 'Vui lòng chọn nhà thuốc để truy cập admin panel.');
});

Route::get('/admin/login', function () {
    return redirect('/tenants')->with('message', 'Vui lòng chọn nhà thuốc để đăng nhập.');
});

// Central domain routes
Route::get('/tenants', function () {
    $tenants = \App\Models\Tenant::all();
    return view('tenants.list', compact('tenants'));
})->name('tenants.list');

// Tenant access routes
Route::prefix('tenant/{tenant_id}')->middleware([
    \App\Http\Middleware\InitializeTenancyByPath::class,
])->group(function () {
    Route::get('/', function () {
        $tenant = tenant();
        return view('tenant.dashboard', compact('tenant'));
    })->name('tenant.dashboard');

    // Redirect to admin with tenant context preserved
    Route::get('/admin', function () {
        return redirect('/admin');
    })->name('tenant.admin');
});
