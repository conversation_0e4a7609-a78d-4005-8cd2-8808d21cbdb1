<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CustomerResource\Pages;
use App\Filament\Resources\CustomerResource\RelationManagers;
use App\Models\Customer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationLabel = 'Khách hàng';

    protected static ?string $modelLabel = 'Khách hàng';

    protected static ?string $pluralModelLabel = 'Khách hàng';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Thông tin cơ bản')
                    ->schema([
                        TextInput::make('code')
                            ->label('Mã khách hàng')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(50),

                        Select::make('type')
                            ->label('Loại khách hàng')
                            ->options([
                                'individual' => 'Cá nhân',
                                'company' => 'Công ty',
                            ])
                            ->default('individual')
                            ->required(),

                        TextInput::make('name')
                            ->label('Tên khách hàng')
                            ->required()
                            ->maxLength(255),

                        TextInput::make('phone')
                            ->label('Số điện thoại')
                            ->tel()
                            ->maxLength(20),

                        TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->maxLength(255),

                        DatePicker::make('date_of_birth')
                            ->label('Ngày sinh')
                            ->maxDate(now()),

                        Select::make('gender')
                            ->label('Giới tính')
                            ->options([
                                'male' => 'Nam',
                                'female' => 'Nữ',
                                'other' => 'Khác',
                            ]),

                        TextInput::make('id_number')
                            ->label('CMND/CCCD')
                            ->maxLength(20),
                    ])
                    ->columns(2),

                Section::make('Địa chỉ')
                    ->schema([
                        Textarea::make('address')
                            ->label('Địa chỉ')
                            ->rows(2),

                        TextInput::make('ward')
                            ->label('Phường/Xã')
                            ->maxLength(100),

                        TextInput::make('district')
                            ->label('Quận/Huyện')
                            ->maxLength(100),

                        TextInput::make('province')
                            ->label('Tỉnh/Thành phố')
                            ->maxLength(100),
                    ])
                    ->columns(2),

                Section::make('Thông tin khác')
                    ->schema([
                        Select::make('customer_group')
                            ->label('Nhóm khách hàng')
                            ->options([
                                'retail' => 'Bán lẻ',
                                'wholesale' => 'Bán sỉ',
                                'vip' => 'VIP',
                            ])
                            ->default('retail'),

                        TextInput::make('tax_code')
                            ->label('Mã số thuế')
                            ->maxLength(20),

                        TextInput::make('company_name')
                            ->label('Tên công ty')
                            ->maxLength(255),

                        Select::make('status')
                            ->label('Trạng thái')
                            ->options([
                                'active' => 'Hoạt động',
                                'inactive' => 'Không hoạt động',
                                'blocked' => 'Bị khóa',
                            ])
                            ->default('active'),

                        Textarea::make('notes')
                            ->label('Ghi chú')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->label('Mã KH')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('name')
                    ->label('Tên khách hàng')
                    ->searchable()
                    ->sortable()
                    ->limit(30),

                TextColumn::make('phone')
                    ->label('Điện thoại')
                    ->searchable(),

                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->limit(30)
                    ->toggleable(),

                BadgeColumn::make('customer_group')
                    ->label('Nhóm')
                    ->colors([
                        'success' => 'vip',
                        'warning' => 'wholesale',
                        'primary' => 'retail',
                    ]),

                TextColumn::make('total_spent')
                    ->label('Tổng chi tiêu')
                    ->money('VND')
                    ->sortable(),

                BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'success' => 'active',
                        'warning' => 'inactive',
                        'danger' => 'blocked',
                    ]),

                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('customer_group')
                    ->label('Nhóm khách hàng')
                    ->options([
                        'retail' => 'Bán lẻ',
                        'wholesale' => 'Bán sỉ',
                        'vip' => 'VIP',
                    ]),

                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'active' => 'Hoạt động',
                        'inactive' => 'Không hoạt động',
                        'blocked' => 'Bị khóa',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
        ];
    }
}
