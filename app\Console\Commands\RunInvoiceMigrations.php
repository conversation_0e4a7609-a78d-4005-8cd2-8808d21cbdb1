<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\Tenant;

class RunInvoiceMigrations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invoice:migrate {--tenant=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run invoice-related migrations for tenants';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tenantId = $this->option('tenant');

        if ($tenantId) {
            $tenant = Tenant::find($tenantId);
            if (!$tenant) {
                $this->error("Tenant {$tenantId} not found");
                return 1;
            }
            $this->runMigrationsForTenant($tenant);
        } else {
            $tenants = Tenant::all();
            foreach ($tenants as $tenant) {
                $this->runMigrationsForTenant($tenant);
            }
        }

        return 0;
    }

    private function runMigrationsForTenant(Tenant $tenant)
    {
        $this->info("Running migrations for tenant: {$tenant->id}");

        $tenant->run(function () {
            $this->createBatchesTable();
            $this->createInvoicesTable();
            $this->createInvoiceItemsTable();
        });

        $this->info("Migrations completed for tenant: {$tenant->id}");
    }

    private function createBatchesTable()
    {
        if (!Schema::hasTable('batches')) {
            $this->info('Creating batches table...');
            Schema::create('batches', function ($table) {
                $table->id();
                $table->foreignId('drug_id')->constrained()->onDelete('cascade');
                $table->string('batch_number', 50)->comment('Số lô');
                $table->date('manufacture_date')->nullable()->comment('Ngày sản xuất');
                $table->date('expiry_date')->comment('Hạn sử dụng');
                $table->date('import_date')->comment('Ngày nhập kho');
                $table->decimal('import_price', 15, 2)->comment('Giá nhập');
                $table->decimal('selling_price', 15, 2)->comment('Giá bán');
                $table->integer('initial_quantity')->comment('Số lượng nhập ban đầu');
                $table->integer('current_quantity')->comment('Số lượng hiện tại');
                $table->integer('reserved_quantity')->default(0)->comment('Số lượng đã đặt');
                $table->foreignId('supplier_id')->nullable()->comment('Nhà cung cấp');
                $table->string('import_invoice_number', 50)->nullable()->comment('Số hóa đơn nhập');
                $table->text('notes')->nullable()->comment('Ghi chú');
                $table->boolean('is_active')->default(true);
                $table->timestamps();

                // Indexes
                $table->index(['drug_id']);
                $table->index(['expiry_date']);
                $table->index(['current_quantity']);
                $table->index(['batch_number']);
                $table->unique(['drug_id', 'batch_number'], 'uk_batches_drug_batch');
            });
        }
    }

    private function createInvoicesTable()
    {
        if (!Schema::hasTable('invoices')) {
            $this->info('Creating invoices table...');
            Schema::create('invoices', function ($table) {
                $table->id();
                $table->string('invoice_number', 50)->unique()->comment('Số hóa đơn');
                $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->dateTime('invoice_date')->comment('Ngày xuất hóa đơn');
                $table->decimal('subtotal', 15, 2)->default(0)->comment('Tổng tiền hàng');
                $table->decimal('discount_amount', 15, 2)->default(0)->comment('Tiền giảm giá');
                $table->decimal('tax_amount', 15, 2)->default(0)->comment('Tiền thuế');
                $table->decimal('total_amount', 15, 2)->default(0)->comment('Tổng tiền thanh toán');
                $table->enum('payment_method', ['cash', 'card', 'transfer', 'qr_code'])->default('cash');
                $table->enum('payment_status', ['pending', 'paid', 'partial', 'refunded'])->default('paid');
                $table->enum('status', ['draft', 'confirmed', 'cancelled'])->default('draft');
                $table->uuid('uuid')->unique()->comment('UUID cho customer portal');
                $table->enum('tax_authority_status', ['pending', 'sent', 'confirmed', 'failed'])->nullable();
                $table->string('tax_transaction_id', 100)->nullable()->comment('Mã giao dịch cơ quan thuế');
                $table->text('digital_signature')->nullable()->comment('Chữ ký số');
                $table->text('notes')->nullable()->comment('Ghi chú');
                $table->text('qr_code_data')->nullable()->comment('Dữ liệu QR code');
                $table->timestamps();
                $table->softDeletes();

                // Indexes
                $table->index(['invoice_date']);
                $table->index(['status']);
                $table->index(['payment_status']);
                $table->index(['customer_id']);
                $table->index(['user_id']);
                $table->index(['created_at']);
            });
        }
    }

    private function createInvoiceItemsTable()
    {
        if (!Schema::hasTable('invoice_items')) {
            $this->info('Creating invoice_items table...');
            Schema::create('invoice_items', function ($table) {
                $table->id();
                $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
                $table->foreignId('batch_id')->constrained()->onDelete('cascade');
                $table->integer('quantity')->comment('Số lượng');
                $table->decimal('unit_price', 15, 2)->comment('Đơn giá');
                $table->decimal('discount_percentage', 5, 2)->default(0)->comment('% giảm giá');
                $table->decimal('discount_amount', 15, 2)->default(0)->comment('Tiền giảm giá');
                $table->decimal('tax_rate', 5, 2)->comment('% thuế');
                $table->decimal('tax_amount', 15, 2)->default(0)->comment('Tiền thuế');
                $table->decimal('total_price', 15, 2)->comment('Thành tiền');
                $table->timestamps();

                // Indexes
                $table->index(['invoice_id']);
                $table->index(['batch_id']);
            });
        }
    }
}
