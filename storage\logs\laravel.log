[2025-06-10 05:45:52] local.ERROR: could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) at D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#2 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(522): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(59): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(28): Illuminate\\Database\\SQLiteConnection->configureForeignKeyConstraints()
#5 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(277): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'D:\\\\laragon\\\\www\\\\...', '', Array)
#6 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'D:\\\\laragon\\\\www\\\\...', '', Array)
#7 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(51): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(207): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#9 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(103): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#10 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(226): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(182): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getConnection()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(742): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#13 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(165): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#14 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#15 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(165): retry(1, Object(Closure), 0, Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(141): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#17 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(111): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#18 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#19 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#21 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#27 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#35 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('sqlite:D:\\\\larag...', NULL, Object(SensitiveParameterValue), Array)
#1 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:D:\\\\larag...', NULL, Object(SensitiveParameterValue), Array)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(40): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:D:\\\\larag...', Array, Array)
#3 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(565): Illuminate\\Database\\Connection->getPdo()
#7 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('PRAGMA foreign_...', Array)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(522): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(59): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(28): Illuminate\\Database\\SQLiteConnection->configureForeignKeyConstraints()
#13 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(277): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'D:\\\\laragon\\\\www\\\\...', '', Array)
#14 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'D:\\\\laragon\\\\www\\\\...', '', Array)
#15 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(51): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#16 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(207): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#17 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(103): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#18 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(226): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#19 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(182): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getConnection()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(742): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#21 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(165): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#23 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(165): retry(1, Object(Closure), 0, Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(141): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#25 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(111): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#26 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#27 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#29 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#30 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#31 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#34 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#35 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#43 {main}
"} 
[2025-06-10 05:50:38] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'pharmasaas'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `sessions` where `id` = bVCS3s2yw1SUtKcz7pjqyehTM0rQiUBpRj3qzQv5 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'pharmasaas'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `sessions` where `id` = bVCS3s2yw1SUtKcz7pjqyehTM0rQiUBpRj3qzQv5 limit 1) at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('bVCS3s2yw1SUtKc...')
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('bVCS3s2yw1SUtKc...')
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(89): Illuminate\\Session\\Store->loadSession()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'pharmasaas'@'localhost' (using password: YES) at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'pharmasaas', Object(SensitiveParameterValue), Array)
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'pharmasaas', Object(SensitiveParameterValue), Array)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getPdo()
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('bVCS3s2yw1SUtKc...')
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('bVCS3s2yw1SUtKc...')
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(89): Illuminate\\Session\\Store->loadSession()
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 {main}
"} 
[2025-06-10 05:50:50] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'pharmasaas_central.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = hqOien6Upxp1huWonDjLsiYg2CO1Q2AvtjBKmaTk limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'pharmasaas_central.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = hqOien6Upxp1huWonDjLsiYg2CO1Q2AvtjBKmaTk limit 1) at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('hqOien6Upxp1huW...')
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('hqOien6Upxp1huW...')
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(89): Illuminate\\Session\\Store->loadSession()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'pharmasaas_central.sessions' doesn't exist at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:407)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): PDO->prepare('select * from `...')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('hqOien6Upxp1huW...')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('hqOien6Upxp1huW...')
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(89): Illuminate\\Session\\Store->loadSession()
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 {main}
"} 
[2025-06-10 06:18:23] local.ERROR: require(D:\laragon\www\pmql-hieuthuoc\routes/tenant.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): require(D:\\laragon\\www\\pmql-hieuthuoc\\routes/tenant.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php:35)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 35)
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'require(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 35)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(513): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes('D:\\\\laragon\\\\www\\\\...')
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(207): Illuminate\\Routing\\Router->group(Array, 'D:\\\\laragon\\\\www\\\\...')
#6 D:\\laragon\\www\\pmql-hieuthuoc\\bootstrap\\app.php(17): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\laragon\\\\www\\\\...')
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(263): {closure}(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}()
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(162): Illuminate\\Container\\Container->call(Object(Closure))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(144): Illuminate\\Container\\Container->call(Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1154): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider))
#23 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider), 'Illuminate\\\\Foun...')
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}
"} 
[2025-06-10 13:28:07] local.ERROR: Tenant could not be identified on domain pmql-hieuthuoc.test {"exception":"[object] (Stancl\\Tenancy\\Exceptions\\TenantCouldNotBeIdentifiedOnDomainException(code: 0): Tenant could not be identified on domain pmql-hieuthuoc.test at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\DomainTenantResolver.php:48)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\Contracts\\CachedTenantResolver.php(34): Stancl\\Tenancy\\Resolvers\\DomainTenantResolver->resolveWithoutCache('pmql-hieuthuoc....')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\IdentificationMiddleware.php(26): Stancl\\Tenancy\\Resolvers\\Contracts\\CachedTenantResolver->resolve('pmql-hieuthuoc....')
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\InitializeTenancyByDomain.php(37): Stancl\\Tenancy\\Middleware\\IdentificationMiddleware->initializeTenancy(Object(Illuminate\\Http\\Request), Object(Closure), 'pmql-hieuthuoc....')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\InitializeTenancyByDomain->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\PreventAccessFromCentralDomains.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\PreventAccessFromCentralDomains->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 {main}
"} 
[2025-06-10 13:32:40] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'pharmasaas_central.tenants' doesn't exist (Connection: mysql, SQL: select * from `tenants` where exists (select * from `domains` where `tenants`.`id` = `domains`.`tenant_id` and `domain` = pmql-hieuthuoc.test) limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'pharmasaas_central.tenants' doesn't exist (Connection: mysql, SQL: select * from `tenants` where exists (select * from `domains` where `tenants`.`id` = `domains`.`tenant_id` and `domain` = pmql-hieuthuoc.test) limit 1) at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\DomainTenantResolver.php(40): Illuminate\\Database\\Eloquent\\Builder->first()
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\Contracts\\CachedTenantResolver.php(34): Stancl\\Tenancy\\Resolvers\\DomainTenantResolver->resolveWithoutCache('pmql-hieuthuoc....')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\IdentificationMiddleware.php(26): Stancl\\Tenancy\\Resolvers\\Contracts\\CachedTenantResolver->resolve('pmql-hieuthuoc....')
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\InitializeTenancyByDomain.php(37): Stancl\\Tenancy\\Middleware\\IdentificationMiddleware->initializeTenancy(Object(Illuminate\\Http\\Request), Object(Closure), 'pmql-hieuthuoc....')
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\InitializeTenancyByDomain->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\PreventAccessFromCentralDomains.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\PreventAccessFromCentralDomains->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#44 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'pharmasaas_central.tenants' doesn't exist at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:407)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): PDO->prepare('select * from `...')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\DomainTenantResolver.php(40): Illuminate\\Database\\Eloquent\\Builder->first()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\Contracts\\CachedTenantResolver.php(34): Stancl\\Tenancy\\Resolvers\\DomainTenantResolver->resolveWithoutCache('pmql-hieuthuoc....')
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\IdentificationMiddleware.php(26): Stancl\\Tenancy\\Resolvers\\Contracts\\CachedTenantResolver->resolve('pmql-hieuthuoc....')
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\InitializeTenancyByDomain.php(37): Stancl\\Tenancy\\Middleware\\IdentificationMiddleware->initializeTenancy(Object(Illuminate\\Http\\Request), Object(Closure), 'pmql-hieuthuoc....')
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\InitializeTenancyByDomain->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\PreventAccessFromCentralDomains.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\PreventAccessFromCentralDomains->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 {main}
"} 
[2025-06-10 13:32:51] local.ERROR: Tenant could not be identified on domain pmql-hieuthuoc.test {"exception":"[object] (Stancl\\Tenancy\\Exceptions\\TenantCouldNotBeIdentifiedOnDomainException(code: 0): Tenant could not be identified on domain pmql-hieuthuoc.test at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\DomainTenantResolver.php:48)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\Contracts\\CachedTenantResolver.php(34): Stancl\\Tenancy\\Resolvers\\DomainTenantResolver->resolveWithoutCache('pmql-hieuthuoc....')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\IdentificationMiddleware.php(26): Stancl\\Tenancy\\Resolvers\\Contracts\\CachedTenantResolver->resolve('pmql-hieuthuoc....')
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\InitializeTenancyByDomain.php(37): Stancl\\Tenancy\\Middleware\\IdentificationMiddleware->initializeTenancy(Object(Illuminate\\Http\\Request), Object(Closure), 'pmql-hieuthuoc....')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\InitializeTenancyByDomain->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\PreventAccessFromCentralDomains.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\PreventAccessFromCentralDomains->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 {main}
"} 
[2025-06-10 13:33:00] local.ERROR: Failed to cleanup tenant 95697c5b-b3bb-4b0b-9807-e10295441294: SQLSTATE[HY000]: General error: 1008 Can't drop database 'pharmacy_95697c5b-b3bb-4b0b-9807-e10295441294'; database doesn't exist (Connection: mysql, SQL: DROP DATABASE `pharmacy_95697c5b-b3bb-4b0b-9807-e10295441294`)  
[2025-06-10 13:33:36] local.ERROR: Failed to cleanup tenant 8b882e20-d06c-4d85-bdaf-791a88ce66a3: SQLSTATE[HY000]: General error: 1008 Can't drop database 'pharmacy_8b882e20-d06c-4d85-bdaf-791a88ce66a3'; database doesn't exist (Connection: mysql, SQL: DROP DATABASE `pharmacy_8b882e20-d06c-4d85-bdaf-791a88ce66a3`)  
[2025-06-10 13:40:06] local.ERROR: Failed to cleanup tenant 8f349416-ab25-44b1-9fd3-7413e0274231: SQLSTATE[HY000]: General error: 1008 Can't drop database 'pharmacy_8f349416-ab25-44b1-9fd3-7413e0274231'; database doesn't exist (Connection: mysql, SQL: DROP DATABASE `pharmacy_8f349416-ab25-44b1-9fd3-7413e0274231`)  
[2025-06-10 13:43:59] local.ERROR: Failed to cleanup tenant 4b667232-67d2-4049-b34f-5dc019bcb9b3: SQLSTATE[HY000]: General error: 1008 Can't drop database 'pharmacy_4b667232-67d2-4049-b34f-5dc019bcb9b3'; database doesn't exist (Connection: mysql, SQL: DROP DATABASE `pharmacy_4b667232-67d2-4049-b34f-5dc019bcb9b3`)  
[2025-06-10 13:44:56] local.ERROR: Failed to cleanup tenant e6c2c3cb-166a-4c44-a116-5d80ee48fecf: SQLSTATE[HY000]: General error: 1008 Can't drop database 'pharmacy_e6c2c3cb-166a-4c44-a116-5d80ee48fecf'; database doesn't exist (Connection: mysql, SQL: DROP DATABASE `pharmacy_e6c2c3cb-166a-4c44-a116-5d80ee48fecf`)  
[2025-06-10 13:53:49] local.ERROR: Call to a member function format() on string {"exception":"[object] (Error(code: 0): Call to a member function format() on string at D:\\laragon\\www\\pmql-hieuthuoc\\app\\Console\\Commands\\CreateTenantCommand.php:68)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\CreateTenantCommand->handle(Object(App\\Services\\TenantService))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\CreateTenantCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-06-10 14:04:24] local.ERROR: Call to a member function format() on string {"exception":"[object] (Error(code: 0): Call to a member function format() on string at D:\\laragon\\www\\pmql-hieuthuoc\\app\\Console\\Commands\\CreateTenantCommand.php:68)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\CreateTenantCommand->handle(Object(App\\Services\\TenantService))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\CreateTenantCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-06-10 14:07:56] local.ERROR: Tenant could not be identified on domain pmql-hieuthuoc.test {"exception":"[object] (Stancl\\Tenancy\\Exceptions\\TenantCouldNotBeIdentifiedOnDomainException(code: 0): Tenant could not be identified on domain pmql-hieuthuoc.test at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\DomainTenantResolver.php:48)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\Contracts\\CachedTenantResolver.php(34): Stancl\\Tenancy\\Resolvers\\DomainTenantResolver->resolveWithoutCache('pmql-hieuthuoc....')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\IdentificationMiddleware.php(26): Stancl\\Tenancy\\Resolvers\\Contracts\\CachedTenantResolver->resolve('pmql-hieuthuoc....')
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\InitializeTenancyByDomain.php(37): Stancl\\Tenancy\\Middleware\\IdentificationMiddleware->initializeTenancy(Object(Illuminate\\Http\\Request), Object(Closure), 'pmql-hieuthuoc....')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\InitializeTenancyByDomain->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\PreventAccessFromCentralDomains.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\PreventAccessFromCentralDomains->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 {main}
"} 
[2025-06-10 14:07:57] local.ERROR: Tenant could not be identified on domain pmql-hieuthuoc.test {"exception":"[object] (Stancl\\Tenancy\\Exceptions\\TenantCouldNotBeIdentifiedOnDomainException(code: 0): Tenant could not be identified on domain pmql-hieuthuoc.test at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\DomainTenantResolver.php:48)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\Contracts\\CachedTenantResolver.php(34): Stancl\\Tenancy\\Resolvers\\DomainTenantResolver->resolveWithoutCache('pmql-hieuthuoc....')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\IdentificationMiddleware.php(26): Stancl\\Tenancy\\Resolvers\\Contracts\\CachedTenantResolver->resolve('pmql-hieuthuoc....')
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\InitializeTenancyByDomain.php(37): Stancl\\Tenancy\\Middleware\\IdentificationMiddleware->initializeTenancy(Object(Illuminate\\Http\\Request), Object(Closure), 'pmql-hieuthuoc....')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\InitializeTenancyByDomain->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\PreventAccessFromCentralDomains.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\PreventAccessFromCentralDomains->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 {main}
"} 
[2025-06-10 14:36:34] local.ERROR: Tenant could not be identified on domain pmql-hieuthuoc.test {"exception":"[object] (Stancl\\Tenancy\\Exceptions\\TenantCouldNotBeIdentifiedOnDomainException(code: 0): Tenant could not be identified on domain pmql-hieuthuoc.test at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\DomainTenantResolver.php:48)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\Contracts\\CachedTenantResolver.php(34): Stancl\\Tenancy\\Resolvers\\DomainTenantResolver->resolveWithoutCache('pmql-hieuthuoc....')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\IdentificationMiddleware.php(26): Stancl\\Tenancy\\Resolvers\\Contracts\\CachedTenantResolver->resolve('pmql-hieuthuoc....')
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\InitializeTenancyByDomain.php(37): Stancl\\Tenancy\\Middleware\\IdentificationMiddleware->initializeTenancy(Object(Illuminate\\Http\\Request), Object(Closure), 'pmql-hieuthuoc....')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\InitializeTenancyByDomain->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\PreventAccessFromCentralDomains.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\PreventAccessFromCentralDomains->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#36 {main}
"} 
[2025-06-10 14:47:13] local.ERROR: The "--path" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--path\" option does not exist. at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Input\\ArgvInput.php(153): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('path', 'database/migrat...')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--path=database...')
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--path=database...', true)
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(276): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Stancl\\Tenancy\\Commands\\Run), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-06-10 14:47:23] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (Connection: tenant, SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `email` varchar(255) not null, `email_verified_at` timestamp null, `password` varchar(255) not null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (Connection: tenant, SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `email` varchar(255) not null, `email_verified_at` timestamp null, `password` varchar(255) not null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `u...', Array, Object(Closure))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `u...', Array, Object(Closure))
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `u...')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\database\\migrations\\0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\laragon\\\\www\\\\...', 2, false)
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(54): Illuminate\\Console\\Command->call('migrate', Array)
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Tenancy.php(160): Stancl\\Tenancy\\Commands\\Run->Stancl\\Tenancy\\Commands\\{closure}(Object(App\\Models\\Tenant))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(35): Stancl\\Tenancy\\Tenancy->runForMultiple(Object(Illuminate\\Support\\LazyCollection), Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Stancl\\Tenancy\\Commands\\Run->handle()
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Stancl\\Tenancy\\Commands\\Run), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#47 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:571)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(571): PDOStatement->execute()
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `u...', Array)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `u...', Array, Object(Closure))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `u...', Array, Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `u...')
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\database\\migrations\\0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\laragon\\\\www\\\\...', 2, false)
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(54): Illuminate\\Console\\Command->call('migrate', Array)
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Tenancy.php(160): Stancl\\Tenancy\\Commands\\Run->Stancl\\Tenancy\\Commands\\{closure}(Object(App\\Models\\Tenant))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(35): Stancl\\Tenancy\\Tenancy->runForMultiple(Object(Illuminate\\Support\\LazyCollection), Object(Closure))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Stancl\\Tenancy\\Commands\\Run->handle()
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Stancl\\Tenancy\\Commands\\Run), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-06-10 14:47:37] local.ERROR: Command "migrate --path=database/migrations/tenant" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"migrate --path=database/migrations/tenant\" is not defined. at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(247): Symfony\\Component\\Console\\Application->find('migrate --path=...')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->resolveCommand('migrate --path=...')
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate --path=...', Array, Object(Illuminate\\Console\\OutputStyle))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(54): Illuminate\\Console\\Command->call('migrate --path=...', Array)
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Tenancy.php(160): Stancl\\Tenancy\\Commands\\Run->Stancl\\Tenancy\\Commands\\{closure}(Object(App\\Models\\Tenant))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(35): Stancl\\Tenancy\\Tenancy->runForMultiple(Object(Illuminate\\Support\\LazyCollection), Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Stancl\\Tenancy\\Commands\\Run->handle()
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Stancl\\Tenancy\\Commands\\Run), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-06-10 15:37:17] local.ERROR: Tenant could not be identified on domain pmql-hieuthuoc.test {"exception":"[object] (Stancl\\Tenancy\\Exceptions\\TenantCouldNotBeIdentifiedOnDomainException(code: 0): Tenant could not be identified on domain pmql-hieuthuoc.test at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\DomainTenantResolver.php:48)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\Contracts\\CachedTenantResolver.php(34): Stancl\\Tenancy\\Resolvers\\DomainTenantResolver->resolveWithoutCache('pmql-hieuthuoc....')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\IdentificationMiddleware.php(26): Stancl\\Tenancy\\Resolvers\\Contracts\\CachedTenantResolver->resolve('pmql-hieuthuoc....')
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\InitializeTenancyByDomain.php(37): Stancl\\Tenancy\\Middleware\\IdentificationMiddleware->initializeTenancy(Object(Illuminate\\Http\\Request), Object(Closure), 'pmql-hieuthuoc....')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\InitializeTenancyByDomain->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\PreventAccessFromCentralDomains.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\PreventAccessFromCentralDomains->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#36 {main}
"} 
[2025-06-10 16:37:38] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'users.users_email_unique' (Connection: tenant, SQL: insert into `users` (`name`, `email`, `password`, `role`, `is_active`, `updated_at`, `created_at`) values (Admin ABC, <EMAIL>, $2y$12$u.D5VpBlDmQe9lkOcpVm1.fn04m89pBtIftbjdq533HpDKQKPbfCq, owner, 1, 2025-06-10 16:37:38, 2025-06-10 16:37:38)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'users.users_email_unique' (Connection: tenant, SQL: insert into `users` (`name`, `email`, `password`, `role`, `is_active`, `updated_at`, `created_at`) values (Admin ABC, <EMAIL>, $2y$12$u.D5VpBlDmQe9lkOcpVm1.fn04m89pBtIftbjdq533HpDKQKPbfCq, owner, 1, 2025-06-10 16:37:38, 2025-06-10 16:37:38)) at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:820)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into `us...', Array, Object(Closure))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `us...', Array, Object(Closure))
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `us...', Array, 'id')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3766): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `us...', Array, 'id')
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2120): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1128): Illuminate\\Database\\Eloquent\\Model->save()
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1127): tap(Object(App\\Models\\User), Object(Closure))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2372): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2384): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 D:\\laragon\\www\\pmql-hieuthuoc\\app\\Console\\Commands\\CreateAdminUserCommand.php(55): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Database\\Concerns\\TenantRun.php(24): App\\Console\\Commands\\CreateAdminUserCommand->App\\Console\\Commands\\{closure}(Object(App\\Models\\Tenant))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\app\\Console\\Commands\\CreateAdminUserCommand.php(54): Stancl\\Tenancy\\Database\\Models\\Tenant->run(Object(Closure))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\CreateAdminUserCommand->handle()
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\CreateAdminUserCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'users.users_email_unique' at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `us...', Array)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into `us...', Array, Object(Closure))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `us...', Array, Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `us...', Array, 'id')
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3766): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `us...', Array, 'id')
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2120): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1128): Illuminate\\Database\\Eloquent\\Model->save()
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1127): tap(Object(App\\Models\\User), Object(Closure))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2372): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2384): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 D:\\laragon\\www\\pmql-hieuthuoc\\app\\Console\\Commands\\CreateAdminUserCommand.php(55): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Database\\Concerns\\TenantRun.php(24): App\\Console\\Commands\\CreateAdminUserCommand->App\\Console\\Commands\\{closure}(Object(App\\Models\\Tenant))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\app\\Console\\Commands\\CreateAdminUserCommand.php(54): Stancl\\Tenancy\\Database\\Models\\Tenant->run(Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\CreateAdminUserCommand->handle()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\CreateAdminUserCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#33 {main}
"} 
[2025-06-10 17:16:26] local.ERROR: SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'drugs' (Connection: mysql, SQL: alter table `batches` add constraint `batches_drug_id_foreign` foreign key (`drug_id`) references `drugs` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'drugs' (Connection: mysql, SQL: alter table `batches` add constraint `batches_drug_id_foreign` foreign key (`drug_id`) references `drugs` (`id`) on delete cascade) at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('alter table `ba...', Array, Object(Closure))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('alter table `ba...', Array, Object(Closure))
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('alter table `ba...')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('batches', Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\database\\migrations\\2025_06_10_171025_create_batches_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_06_10_1710...', Object(Closure))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_10_1710...', Object(Closure))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\laragon\\\\www\\\\...', 3, false)
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'drugs' at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:571)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(571): PDOStatement->execute()
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `ba...', Array)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('alter table `ba...', Array, Object(Closure))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('alter table `ba...', Array, Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('alter table `ba...')
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('batches', Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\database\\migrations\\2025_06_10_171025_create_batches_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_06_10_1710...', Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_10_1710...', Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\laragon\\\\www\\\\...', 3, false)
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-10 17:21:26] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (Connection: tenant, SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `email` varchar(255) not null, `email_verified_at` timestamp null, `password` varchar(255) not null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (Connection: tenant, SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `email` varchar(255) not null, `email_verified_at` timestamp null, `password` varchar(255) not null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `u...', Array, Object(Closure))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `u...', Array, Object(Closure))
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `u...')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\database\\migrations\\0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\laragon\\\\www\\\\...', 4, false)
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(54): Illuminate\\Console\\Command->call('migrate', Array)
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Tenancy.php(160): Stancl\\Tenancy\\Commands\\Run->Stancl\\Tenancy\\Commands\\{closure}(Object(App\\Models\\Tenant))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(35): Stancl\\Tenancy\\Tenancy->runForMultiple(Array, Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Stancl\\Tenancy\\Commands\\Run->handle()
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Stancl\\Tenancy\\Commands\\Run), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#47 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:571)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(571): PDOStatement->execute()
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `u...', Array)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `u...', Array, Object(Closure))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `u...', Array, Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `u...')
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\database\\migrations\\0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\laragon\\\\www\\\\...', 4, false)
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(54): Illuminate\\Console\\Command->call('migrate', Array)
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Tenancy.php(160): Stancl\\Tenancy\\Commands\\Run->Stancl\\Tenancy\\Commands\\{closure}(Object(App\\Models\\Tenant))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(35): Stancl\\Tenancy\\Tenancy->runForMultiple(Array, Object(Closure))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Stancl\\Tenancy\\Commands\\Run->handle()
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Stancl\\Tenancy\\Commands\\Run), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-06-10 17:23:28] local.ERROR: Command "migrate --path=database/migrations/tenant/2025_06_10_171025_create_batches_table.php" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"migrate --path=database/migrations/tenant/2025_06_10_171025_create_batches_table.php\" is not defined. at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(247): Symfony\\Component\\Console\\Application->find('migrate --path=...')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->resolveCommand('migrate --path=...')
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate --path=...', Array, Object(Illuminate\\Console\\OutputStyle))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(54): Illuminate\\Console\\Command->call('migrate --path=...', Array)
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Tenancy.php(160): Stancl\\Tenancy\\Commands\\Run->Stancl\\Tenancy\\Commands\\{closure}(Object(App\\Models\\Tenant))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(35): Stancl\\Tenancy\\Tenancy->runForMultiple(Array, Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Stancl\\Tenancy\\Commands\\Run->handle()
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Stancl\\Tenancy\\Commands\\Run), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-06-10 17:23:41] local.ERROR: Command "migrate --force" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"migrate --force\" is not defined. at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(247): Symfony\\Component\\Console\\Application->find('migrate --force')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->resolveCommand('migrate --force')
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate --force', Array, Object(Illuminate\\Console\\OutputStyle))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(54): Illuminate\\Console\\Command->call('migrate --force', Array)
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Tenancy.php(160): Stancl\\Tenancy\\Commands\\Run->Stancl\\Tenancy\\Commands\\{closure}(Object(App\\Models\\Tenant))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(35): Stancl\\Tenancy\\Tenancy->runForMultiple(Array, Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Stancl\\Tenancy\\Commands\\Run->handle()
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Stancl\\Tenancy\\Commands\\Run), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-06-10 17:23:58] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (Connection: tenant, SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `email` varchar(255) not null, `email_verified_at` timestamp null, `password` varchar(255) not null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (Connection: tenant, SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `email` varchar(255) not null, `email_verified_at` timestamp null, `password` varchar(255) not null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `u...', Array, Object(Closure))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `u...', Array, Object(Closure))
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `u...')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\database\\migrations\\0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\laragon\\\\www\\\\...', 4, false)
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(54): Illuminate\\Console\\Command->call('migrate', Array)
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Tenancy.php(160): Stancl\\Tenancy\\Commands\\Run->Stancl\\Tenancy\\Commands\\{closure}(Object(App\\Models\\Tenant))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(35): Stancl\\Tenancy\\Tenancy->runForMultiple(Array, Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Stancl\\Tenancy\\Commands\\Run->handle()
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Stancl\\Tenancy\\Commands\\Run), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#47 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:571)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(571): PDOStatement->execute()
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `u...', Array)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `u...', Array, Object(Closure))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `u...', Array, Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `u...')
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\database\\migrations\\0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\laragon\\\\www\\\\...', 4, false)
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(54): Illuminate\\Console\\Command->call('migrate', Array)
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Tenancy.php(160): Stancl\\Tenancy\\Commands\\Run->Stancl\\Tenancy\\Commands\\{closure}(Object(App\\Models\\Tenant))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Commands\\Run.php(35): Stancl\\Tenancy\\Tenancy->runForMultiple(Array, Object(Closure))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Stancl\\Tenancy\\Commands\\Run->handle()
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Stancl\\Tenancy\\Commands\\Run), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-06-10 17:33:43] local.ERROR: Unable to find component: [App\Filament\Resources\Widgets\SalesStatsWidget] {"exception":"[object] (Livewire\\Exceptions\\ComponentNotFoundException(code: 0): Unable to find component: [App\\Filament\\Resources\\Widgets\\SalesStatsWidget] at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php:116)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(67): Livewire\\Mechanisms\\ComponentRegistry->getNameAndClass('App\\\\Filament\\\\Re...')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(586): Livewire\\Mechanisms\\ComponentRegistry->getName('App\\\\Filament\\\\Re...')
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(542): Filament\\Panel->queueLivewireComponentForRegistration('App\\\\Filament\\\\Re...')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1432): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1350): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(850): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('filament', Array, true)
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('filament', Array)
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('filament', Array)
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1580): Illuminate\\Foundation\\Application->make('filament')
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(207): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('D:\\\\laragon\\\\www\\\\...')
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('D:\\\\laragon\\\\www\\\\...')
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#39 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#48 {main}
"} 
