<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hóa đơn #{{ $invoice->invoice_number }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-4xl mx-auto px-4">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <div class="flex justify-between items-start">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Hóa đơn #{{ $invoice->invoice_number }}</h1>
                        <p class="text-gray-600 mt-2">{{ tenant()->name ?? '<PERSON><PERSON><PERSON> thuốc' }}</p>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-600"><PERSON><PERSON><PERSON> xuất</div>
                        <div class="font-semibold">{{ $invoice->invoice_date->format('d/m/Y H:i') }}</div>
                    </div>
                </div>
            </div>

            <!-- Customer Info -->
            @if($invoice->customer)
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Thông tin khách hàng</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <div class="text-sm text-gray-600">Tên khách hàng</div>
                        <div class="font-semibold">{{ $invoice->customer->name }}</div>
                    </div>
                    @if($invoice->customer->phone)
                    <div>
                        <div class="text-sm text-gray-600">Số điện thoại</div>
                        <div class="font-semibold">{{ $invoice->customer->phone }}</div>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Invoice Items -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Chi tiết hóa đơn</h2>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left py-2">Tên thuốc</th>
                                <th class="text-center py-2">Số lượng</th>
                                <th class="text-right py-2">Đơn giá</th>
                                <th class="text-right py-2">Thành tiền</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($invoice->items as $item)
                            <tr class="border-b">
                                <td class="py-3">
                                    <div class="font-semibold">{{ $item->batch->drug->name }}</div>
                                    <div class="text-sm text-gray-600">Lô: {{ $item->batch->batch_number }}</div>
                                </td>
                                <td class="text-center py-3">{{ $item->quantity }}</td>
                                <td class="text-right py-3">{{ number_format($item->unit_price) }}₫</td>
                                <td class="text-right py-3 font-semibold">{{ number_format($item->total_price) }}₫</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Invoice Summary -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Tổng kết</h2>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span>Tổng tiền hàng:</span>
                        <span>{{ number_format($invoice->subtotal) }}₫</span>
                    </div>
                    @if($invoice->discount_amount > 0)
                    <div class="flex justify-between text-green-600">
                        <span>Giảm giá:</span>
                        <span>-{{ number_format($invoice->discount_amount) }}₫</span>
                    </div>
                    @endif
                    <div class="flex justify-between">
                        <span>Thuế GTGT:</span>
                        <span>{{ number_format($invoice->tax_amount) }}₫</span>
                    </div>
                    <div class="flex justify-between text-xl font-bold border-t pt-2">
                        <span>Tổng thanh toán:</span>
                        <span class="text-blue-600">{{ number_format($invoice->total_amount) }}₫</span>
                    </div>
                </div>
            </div>

            <!-- Payment Info -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Thông tin thanh toán</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <div class="text-sm text-gray-600">Phương thức thanh toán</div>
                        <div class="font-semibold">
                            @switch($invoice->payment_method)
                                @case('cash') Tiền mặt @break
                                @case('card') Thẻ @break
                                @case('transfer') Chuyển khoản @break
                                @case('qr_code') QR Code @break
                                @default {{ $invoice->payment_method }}
                            @endswitch
                        </div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-600">Trạng thái thanh toán</div>
                        <div class="font-semibold">
                            <span class="px-2 py-1 rounded text-sm
                                @if($invoice->payment_status === 'paid') bg-green-100 text-green-800
                                @elseif($invoice->payment_status === 'pending') bg-yellow-100 text-yellow-800
                                @else bg-blue-100 text-blue-800 @endif">
                                @switch($invoice->payment_status)
                                    @case('paid') Đã thanh toán @break
                                    @case('pending') Chờ thanh toán @break
                                    @case('partial') Thanh toán một phần @break
                                    @default {{ $invoice->payment_status }}
                                @endswitch
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- QR Code -->
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <h2 class="text-xl font-semibold mb-4">Mã QR hóa đơn</h2>
                <img src="{{ $invoice->qr_code_url }}" alt="QR Code" class="mx-auto mb-4">
                <p class="text-sm text-gray-600">Quét mã QR để xem hóa đơn này</p>
            </div>

            <!-- Footer -->
            <div class="text-center mt-8 text-gray-600">
                <p>Cảm ơn quý khách đã sử dụng dịch vụ của chúng tôi!</p>
            </div>
        </div>
    </div>
</body>
</html>
