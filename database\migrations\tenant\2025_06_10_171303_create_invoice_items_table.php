<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->foreignId('batch_id')->constrained()->onDelete('cascade');
            $table->integer('quantity')->comment('Số lượng');
            $table->decimal('unit_price', 15, 2)->comment('Đơn giá');
            $table->decimal('discount_percentage', 5, 2)->default(0)->comment('% giảm giá');
            $table->decimal('discount_amount', 15, 2)->default(0)->comment('Tiền giảm giá');
            $table->decimal('tax_rate', 5, 2)->comment('% thuế');
            $table->decimal('tax_amount', 15, 2)->default(0)->comment('Tiền thuế');
            $table->decimal('total_price', 15, 2)->comment('Thành tiền');
            $table->timestamps();

            // Indexes
            $table->index(['invoice_id']);
            $table->index(['batch_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_items');
    }
};
