<?php

namespace App\Services;

use App\Models\Drug;
use App\Models\Batch;

class TaxCalculatorService
{
    /**
     * Calculate VAT for a given amount and drug
     */
    public function calculateVAT(float $amount, Drug $drug): float
    {
        $taxRate = $this->getTaxRate($drug);
        return $amount * ($taxRate / 100);
    }

    /**
     * Get tax rate for a drug (in percentage)
     */
    public function getTaxRate(Drug $drug): float
    {
        // 5% for prescription drugs, 10% for over-the-counter drugs
        return $drug->prescription_required ? 5.0 : 10.0;
    }

    /**
     * Calculate tax for invoice items
     */
    public function calculateInvoiceTax(array $items): array
    {
        $totalTax = 0;
        $taxBreakdown = [];

        foreach ($items as $item) {
            $batch = Batch::with('drug')->find($item['batch_id']);
            if (!$batch) {
                continue;
            }

            $drug = $batch->drug;
            
            // Calculate amounts
            $subtotal = $item['quantity'] * $item['unit_price'];
            $discount = $subtotal * (($item['discount_percentage'] ?? 0) / 100);
            $taxableAmount = $subtotal - $discount;
            
            // Get tax rate and calculate tax
            $taxRate = $this->getTaxRate($drug);
            $taxAmount = $taxableAmount * ($taxRate / 100);
            
            $totalTax += $taxAmount;
            
            $taxBreakdown[] = [
                'drug_name' => $drug->name,
                'batch_number' => $batch->batch_number,
                'quantity' => $item['quantity'],
                'unit_price' => $item['unit_price'],
                'subtotal' => $subtotal,
                'discount' => $discount,
                'taxable_amount' => $taxableAmount,
                'tax_rate' => $taxRate,
                'tax_amount' => $taxAmount,
                'prescription_required' => $drug->prescription_required,
            ];
        }

        return [
            'total_tax' => $totalTax,
            'breakdown' => $taxBreakdown,
            'summary' => $this->getTaxSummary($taxBreakdown),
        ];
    }

    /**
     * Get tax summary by tax rates
     */
    protected function getTaxSummary(array $taxBreakdown): array
    {
        $summary = [
            '5%' => ['taxable_amount' => 0, 'tax_amount' => 0, 'description' => 'Thuốc kê đơn'],
            '10%' => ['taxable_amount' => 0, 'tax_amount' => 0, 'description' => 'Thuốc không kê đơn'],
        ];

        foreach ($taxBreakdown as $item) {
            $rate = $item['tax_rate'] . '%';
            if (isset($summary[$rate])) {
                $summary[$rate]['taxable_amount'] += $item['taxable_amount'];
                $summary[$rate]['tax_amount'] += $item['tax_amount'];
            }
        }

        return $summary;
    }

    /**
     * Validate tax calculation for an invoice
     */
    public function validateInvoiceTax(array $items, float $expectedTax): bool
    {
        $calculation = $this->calculateInvoiceTax($items);
        $tolerance = 0.01; // 1 cent tolerance for rounding differences
        
        return abs($calculation['total_tax'] - $expectedTax) <= $tolerance;
    }

    /**
     * Get tax report for a period
     */
    public function getTaxReport(\DateTime $startDate, \DateTime $endDate): array
    {
        // This would typically query the database for invoices in the period
        // and calculate tax summaries
        
        return [
            'period' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
            ],
            'summary' => [
                'total_revenue' => 0,
                'total_tax' => 0,
                'prescription_drugs' => [
                    'revenue' => 0,
                    'tax' => 0,
                    'rate' => '5%',
                ],
                'otc_drugs' => [
                    'revenue' => 0,
                    'tax' => 0,
                    'rate' => '10%',
                ],
            ],
            'daily_breakdown' => [],
        ];
    }

    /**
     * Calculate tax for a single item
     */
    public function calculateItemTax(int $quantity, float $unitPrice, float $discountPercentage, Drug $drug): array
    {
        $subtotal = $quantity * $unitPrice;
        $discount = $subtotal * ($discountPercentage / 100);
        $taxableAmount = $subtotal - $discount;
        $taxRate = $this->getTaxRate($drug);
        $taxAmount = $taxableAmount * ($taxRate / 100);
        $total = $taxableAmount + $taxAmount;

        return [
            'subtotal' => $subtotal,
            'discount' => $discount,
            'taxable_amount' => $taxableAmount,
            'tax_rate' => $taxRate,
            'tax_amount' => $taxAmount,
            'total' => $total,
        ];
    }
}
