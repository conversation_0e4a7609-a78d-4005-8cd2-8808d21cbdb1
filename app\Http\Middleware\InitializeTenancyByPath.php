<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Tenant;

class InitializeTenancyByPath
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $tenantId = $request->route('tenant_id');

        if (!$tenantId) {
            abort(404, 'Tenant ID not found in path');
        }

        $tenant = Tenant::find($tenantId);

        if (!$tenant) {
            abort(404, 'Tenant not found');
        }

        // Check if tenant can access
        if (!$tenant->canAccess()) {
            if ($tenant->isSuspended()) {
                return response()->view('tenant.suspended', ['tenant' => $tenant], 403);
            }

            if ($tenant->isOnTrial() && $tenant->trial_ends_at->isPast()) {
                return response()->view('tenant.trial-expired', ['tenant' => $tenant], 402);
            }
        }

        // Initialize tenancy
        tenancy()->initialize($tenant);

        return $next($request);
    }
}
