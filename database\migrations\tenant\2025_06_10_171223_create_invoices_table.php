<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number', 50)->unique()->comment('Số hóa đơn');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->dateTime('invoice_date')->comment('Ngày xuất hóa đơn');
            $table->decimal('subtotal', 15, 2)->default(0)->comment('Tổng tiền hàng');
            $table->decimal('discount_amount', 15, 2)->default(0)->comment('Tiền giảm giá');
            $table->decimal('tax_amount', 15, 2)->default(0)->comment('Tiền thuế');
            $table->decimal('total_amount', 15, 2)->default(0)->comment('Tổng tiền thanh toán');
            $table->enum('payment_method', ['cash', 'card', 'transfer', 'qr_code'])->default('cash');
            $table->enum('payment_status', ['pending', 'paid', 'partial', 'refunded'])->default('paid');
            $table->enum('status', ['draft', 'confirmed', 'cancelled'])->default('draft');
            $table->uuid('uuid')->unique()->comment('UUID cho customer portal');
            $table->enum('tax_authority_status', ['pending', 'sent', 'confirmed', 'failed'])->nullable();
            $table->string('tax_transaction_id', 100)->nullable()->comment('Mã giao dịch cơ quan thuế');
            $table->text('digital_signature')->nullable()->comment('Chữ ký số');
            $table->text('notes')->nullable()->comment('Ghi chú');
            $table->text('qr_code_data')->nullable()->comment('Dữ liệu QR code');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['invoice_date']);
            $table->index(['status']);
            $table->index(['payment_status']);
            $table->index(['customer_id']);
            $table->index(['user_id']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
