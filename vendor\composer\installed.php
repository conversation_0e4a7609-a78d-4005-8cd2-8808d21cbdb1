<?php return array(
    'root' => array(
        'name' => 'laravel/laravel',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '14d3d0fda87026fa8e154aa67c415fdf3a70ad55',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(
            0 => '11.x-dev',
        ),
        'dev' => true,
    ),
    'versions' => array(
        'anourvalar/eloquent-serialize' => array(
            'pretty_version' => '1.3.3',
            'version' => '1.3.3.0',
            'reference' => '2f05023f1e465a91dc4f08483e6710325641a444',
            'type' => 'library',
            'install_path' => __DIR__ . '/../anourvalar/eloquent-serialize',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'blade-ui-kit/blade-heroicons' => array(
            'pretty_version' => '2.6.0',
            'version' => '2.6.0.0',
            'reference' => '4553b2a1f6c76f0ac7f3bc0de4c0cfa06a097d19',
            'type' => 'library',
            'install_path' => __DIR__ . '/../blade-ui-kit/blade-heroicons',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'blade-ui-kit/blade-icons' => array(
            'pretty_version' => '1.8.0',
            'version' => '*******',
            'reference' => '7b743f27476acb2ed04cb518213d78abe096e814',
            'type' => 'library',
            'install_path' => __DIR__ . '/../blade-ui-kit/blade-icons',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.12.3',
            'version' => '0.12.3.0',
            'reference' => '866551da34e9a618e64a819ee1e01c20d8a588ba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => '18ba5ddfec8976260ead6e866180bd5d2f71aa1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cordoval/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'danharrin/date-format-converter' => array(
            'pretty_version' => 'v0.3.1',
            'version' => '0.3.1.0',
            'reference' => '7c31171bc981e48726729a5f3a05a2d2b63f0b1e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../danharrin/date-format-converter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'danharrin/livewire-rate-limiting' => array(
            'pretty_version' => 'v2.1.0',
            'version' => '2.1.0.0',
            'reference' => '14dde653a9ae8f38af07a0ba4921dc046235e1a0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../danharrin/livewire-rate-limiting',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'davedevelopment/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'dflydev/dot-access-data' => array(
            'pretty_version' => 'v3.0.3',
            'version' => '3.0.3.0',
            'reference' => 'a23a2bf4f31d3518f3ecb38660c95715dfead60f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/dbal' => array(
            'pretty_version' => '4.2.3',
            'version' => '4.2.3.0',
            'reference' => '33d2d7fe1269b2301640c44cf2896ea607b30e3e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/dbal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dragonmantank/cron-expression' => array(
            'pretty_version' => 'v3.4.0',
            'version' => '3.4.0.0',
            'reference' => '8c784d071debd117328803d86b2097615b457500',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dragonmantank/cron-expression',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => 'd42c8731f0624ad6bdc8d3e5e9a4524f68801cfa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'facade/ignition-contracts' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '3c921a1cdba35b68a7f0ccffc6dffc1995b18267',
            'type' => 'library',
            'install_path' => __DIR__ . '/../facade/ignition-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fakerphp/faker' => array(
            'pretty_version' => 'v1.24.1',
            'version' => '1.24.1.0',
            'reference' => 'e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fakerphp/faker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'filament/actions' => array(
            'pretty_version' => 'v3.3.20',
            'version' => '3.3.20.0',
            'reference' => '151f776552ee10d70591c2649708bc4b0a7cba91',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/actions',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/filament' => array(
            'pretty_version' => 'v3.3.20',
            'version' => '3.3.20.0',
            'reference' => '94ee92244d2a64666fb8c1ea50cb7315ebceb63b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/filament',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/forms' => array(
            'pretty_version' => 'v3.3.20',
            'version' => '3.3.20.0',
            'reference' => 'd73cdda057a4f5bd409eab9573101e73edb404cc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/forms',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/infolists' => array(
            'pretty_version' => 'v3.3.20',
            'version' => '3.3.20.0',
            'reference' => 'b54ff0fa89f654eca1c14edfd41a7e16ccb8165d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/infolists',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/notifications' => array(
            'pretty_version' => 'v3.3.20',
            'version' => '3.3.20.0',
            'reference' => '356f50e24798a6f06522bfa5123c6ffd054171d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/notifications',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/support' => array(
            'pretty_version' => 'v3.3.20',
            'version' => '3.3.20.0',
            'reference' => '4f9793ad3339301ca53ea6f2c984734f7ac38ce7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/support',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/tables' => array(
            'pretty_version' => 'v3.3.20',
            'version' => '3.3.20.0',
            'reference' => '1a107a8411549297b97d1142b1f7a5fa7a65e32b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/tables',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/widgets' => array(
            'pretty_version' => 'v3.3.20',
            'version' => '3.3.20.0',
            'reference' => '048c5a4bf0477efbe2910c54a1aeb55c64cf1348',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/widgets',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filp/whoops' => array(
            'pretty_version' => '2.18.1',
            'version' => '2.18.1.0',
            'reference' => '8fcc6a862f2e7b94eb4221fd0819ddba3d30ab26',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filp/whoops',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'fruitcake/php-cors' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '3d158f36e7875e2f040f37bc0573956240a5a38b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fruitcake/php-cors',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'reference' => '3ba905c11371512af9d9bdd27d99b782216b6945',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/uri-template' => array(
            'pretty_version' => 'v1.0.4',
            'version' => '1.0.4.0',
            'reference' => '30e286560c137526eccd4ce21b2de477ab0676d2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/uri-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hamcrest/hamcrest-php' => array(
            'pretty_version' => 'v2.1.1',
            'version' => '2.1.1.0',
            'reference' => 'f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hamcrest/hamcrest-php',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/auth' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/broadcasting' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/bus' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/collections' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/concurrency' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/conditionable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/config' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/console' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/container' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/contracts' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/cookie' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/database' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/encryption' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/events' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/hashing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/http' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/log' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/macroable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/mail' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/notifications' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/pagination' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/pipeline' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/process' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/queue' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/redis' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/routing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/session' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/support' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/testing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/translation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/validation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/view' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'kirschbaum-development/eloquent-power-joins' => array(
            'pretty_version' => '4.2.4',
            'version' => '4.2.4.0',
            'reference' => '4a8012cef7abed8ac3633a180c69138a228b6c4c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kirschbaum-development/eloquent-power-joins',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kodova/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'laravel/framework' => array(
            'pretty_version' => 'v11.45.1',
            'version' => '11.45.1.0',
            'reference' => 'b09ba32795b8e71df10856a2694706663984a239',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/laravel' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '14d3d0fda87026fa8e154aa67c415fdf3a70ad55',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(
                0 => '11.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'laravel/pint' => array(
            'pretty_version' => 'v1.22.1',
            'version' => '1.22.1.0',
            'reference' => '941d1927c5ca420c22710e98420287169c7bcaf7',
            'type' => 'project',
            'install_path' => __DIR__ . '/../laravel/pint',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laravel/prompts' => array(
            'pretty_version' => 'v0.3.5',
            'version' => '0.3.5.0',
            'reference' => '57b8f7efe40333cdb925700891c7d7465325d3b1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/prompts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/sail' => array(
            'pretty_version' => 'v1.43.1',
            'version' => '1.43.1.0',
            'reference' => '3e7d899232a8c5e3ea4fc6dee7525ad583887e72',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sail',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laravel/sanctum' => array(
            'pretty_version' => 'v4.1.1',
            'version' => '4.1.1.0',
            'reference' => 'a360a6a1fd2400ead4eb9b6a9c1bb272939194f5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sanctum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'b352cf0534aa1ae6b4d825d1e762e35d43f8a841',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/tinker' => array(
            'pretty_version' => 'v2.10.1',
            'version' => '2.10.1.0',
            'reference' => '22177cc71807d38f2810c6204d8f7183d88a57d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/tinker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/commonmark' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'reference' => '6fbb36d44824ed4091adbcf4c7d4a3923cdb3405',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/commonmark',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/config' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => '754b3604fb2984c71f4af4a9cbe7b57f346ec1f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/csv' => array(
            'pretty_version' => '9.23.0',
            'version' => '9.23.0.0',
            'reference' => '774008ad8a634448e4f8e288905e070e8b317ff3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/csv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '3.29.1',
            'version' => '3.29.1.0',
            'reference' => 'edc1bb7c86fab0776c3287dbd19b5fa278347319',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-local' => array(
            'pretty_version' => '3.29.0',
            'version' => '3.29.0.0',
            'reference' => 'e0e8d52ce4b2ed154148453d321e97c8e931bd27',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-local',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.16.0',
            'version' => '1.16.0.0',
            'reference' => '2d6702ff215bf922936ccc1ad31007edc76451b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/uri' => array(
            'pretty_version' => '7.5.1',
            'version' => '7.5.1.0',
            'reference' => '81fb5145d2644324614cc532b28efd0215bda430',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/uri',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/uri-interfaces' => array(
            'pretty_version' => '7.5.0',
            'version' => '7.5.0.0',
            'reference' => '08cfc6c4f3d811584fb09c37e2849e6a7f9b0742',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/uri-interfaces',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'livewire/livewire' => array(
            'pretty_version' => 'v3.6.3',
            'version' => '3.6.3.0',
            'reference' => '56aa1bb63a46e06181c56fa64717a7287e19115e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../livewire/livewire',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '2.9.0.0',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mockery/mockery' => array(
            'pretty_version' => '1.6.12',
            'version' => '1.6.12.0',
            'reference' => '1f4efdd7d3beafe9807b08156dfcb176d18f1699',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mockery/mockery',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.9.0',
            'version' => '3.9.0.0',
            'reference' => '10d85740180ecba7896c87e06a166e0c95a0e3b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/cron-expression' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^1.0',
            ),
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.13.1',
            'version' => '1.13.1.0',
            'reference' => '1720ddd719e16cf0db4eb1c6eca108031636d46c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '3.9.1',
            'version' => '*******',
            'reference' => 'ced71f79398ece168e24f7f7710462f462310d4d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/schema' => array(
            'pretty_version' => 'v1.3.2',
            'version' => '1.3.2.0',
            'reference' => 'da801d52f0354f70a638673c4a0f04e16529431d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/utils' => array(
            'pretty_version' => 'v4.0.7',
            'version' => '4.0.7.0',
            'reference' => 'e67c4061eb40b9c113b218214e42cb5a0dda28f2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.5.0',
            'version' => '5.5.0.0',
            'reference' => 'ae59794362fe85e051a58ad36b289443f57be7a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/collision' => array(
            'pretty_version' => 'v8.5.0',
            'version' => '8.5.0.0',
            'reference' => 'f5c101b929c958e849a633283adff296ed5f38f5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/collision',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v2.3.1',
            'version' => '2.3.1.0',
            'reference' => 'dfa08f390e509967a15c22493dc0bac5733d9123',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'openspout/openspout' => array(
            'pretty_version' => 'v4.30.0',
            'version' => '4.30.0.0',
            'reference' => 'df9b0f4d229c37c3caa5a9252a6ad8a94efb0fb5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../openspout/openspout',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '54750ef60c58e43759730615a392c31c80e23176',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.3',
            'version' => '1.9.3.0',
            'reference' => 'e3fac8b24f56113f7cb96af14958c0dd16330f54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '10.1.16',
            'version' => '10.1.16.0',
            'reference' => '7e308268858ed6baedc8704a304727d20bc07c77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '4.1.0',
            'version' => '4.1.0.0',
            'reference' => 'a95037b6d9e608ba092da1b23931e537cadc3c3c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '4.0.0',
            'version' => '4.0.0.0',
            'reference' => 'f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '0c7b06ff49e3d5072f057eb1fa59258bf287a748',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '6.0.0',
            'version' => '6.0.0.0',
            'reference' => 'e2a2d67966e740530f4a3343fe2e030ffdc1161d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '10.5.46',
            'version' => '10.5.46.0',
            'reference' => '8080be387a5be380dda48c6f41cee4a13aadab3d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
                1 => '3.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.12.8',
            'version' => '0.12.8.0',
            'reference' => '85057ceedee50c49d4f6ecaff73ee96adb3b3625',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '344572933ad0181accbf4ba763e85a0306a8c5e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.8.1',
            'version' => '4.8.1.0',
            'reference' => 'fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.8.1',
            ),
        ),
        'ryangjchandler/blade-capture-directive' => array(
            'pretty_version' => 'v1.1.0',
            'version' => '1.1.0.0',
            'reference' => 'bbb1513dfd89eaec87a47fe0c449a7e3d4a1976d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ryangjchandler/blade-capture-directive',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'c34583b87e7b7a8055bf6c450c2c77ce32a24084',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'a81fee9eef0b7a76af11d121767abc44c104e503',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '5e3a687f7d8ae33fb362c5c0743794bbb2420a1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '5.0.3',
            'version' => '5.0.3.0',
            'reference' => 'a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => '68ff824baeae169ec9f2137158ee529584553799',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '5.1.1',
            'version' => '5.1.1.0',
            'reference' => 'c41e007b4b62af48218231d6c2275e4c9b975b2e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '6.1.0',
            'version' => '6.1.0.0',
            'reference' => '8074dbcd93529b357029f5cc5058fd3e43666984',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '5.1.2',
            'version' => '5.1.2.0',
            'reference' => '955288482d97c19a372d3f31006ab3f37da47adf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'reference' => '987bafff24ecc4c9ac418cab1145b96dd6e9cbd9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => '856e7f6a75a84e339195d48c556f23be2ebf75d0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '5.0.0',
            'version' => '5.0.0.0',
            'reference' => '202d0e344a580d7f7d04b3fafce6933e59dae906',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '24ed13d98130f0e7122df55d06c5c4942a577957',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '5.0.0',
            'version' => '5.0.0.0',
            'reference' => '05909fb5bc7df4c52992396d0116aed689f93712',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '4.0.0',
            'version' => '4.0.0.0',
            'reference' => '462699a16464c3944eefc02ebdd77882bd3925bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => 'c51fa83a5d8f43f1402e3f32a005e6262244ef17',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/backtrace' => array(
            'pretty_version' => '1.7.4',
            'version' => '1.7.4.0',
            'reference' => 'cd37a49fce7137359ac30ecc44ef3e16404cccbe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/backtrace',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/color' => array(
            'pretty_version' => '1.8.0',
            'version' => '*******',
            'reference' => '142af7fec069a420babea80a5412eb2f646dcd8c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/color',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/error-solutions' => array(
            'pretty_version' => '1.1.3',
            'version' => '1.1.3.0',
            'reference' => 'e495d7178ca524f2dd0fe6a1d99a1e608e1c9936',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/error-solutions',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/flare-client-php' => array(
            'pretty_version' => '1.10.1',
            'version' => '1.10.1.0',
            'reference' => 'bf1716eb98bd689451b071548ae9e70738dce62f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/flare-client-php',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/ignition' => array(
            'pretty_version' => '1.15.1',
            'version' => '1.15.1.0',
            'reference' => '31f314153020aee5af3537e507fef892ffbf8c85',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/ignition',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/invade' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => 'b920f6411d21df4e8610a138e2e87ae4957d7f63',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/invade',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-ignition' => array(
            'pretty_version' => '2.9.1',
            'version' => '2.9.1.0',
            'reference' => '1baee07216d6748ebd3a65ba97381b051838707a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-ignition',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/laravel-package-tools' => array(
            'pretty_version' => '1.92.4',
            'version' => '1.92.4.0',
            'reference' => 'd20b1969f836d210459b78683d85c9cd5c5f508c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-package-tools',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-permission' => array(
            'pretty_version' => '6.19.0',
            'version' => '********',
            'reference' => '0cd412dcad066d75caf0b977716809be7e7642fd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-permission',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/once' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'stancl/jobpipeline' => array(
            'pretty_version' => 'v1.8.0',
            'version' => '*******',
            'reference' => 'a987f8ff698456bb807937225b1a98fd92ce3124',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stancl/jobpipeline',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stancl/tenancy' => array(
            'pretty_version' => 'v3.9.1',
            'version' => '*******',
            'reference' => 'd98a170fbd2e114604bfec3bc6267a3d6e02dec1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stancl/tenancy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stancl/virtualcolumn' => array(
            'pretty_version' => 'v1.5.0',
            'version' => '*******',
            'reference' => '75718edcfeeb19abc1970f5395043f7d43cce5bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stancl/virtualcolumn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/clock' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => 'b81435fbd6648ea425d1ee96a2d8e68f4ceacd24',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => '66c1440edf6f339fd82ed6c7caa76cb006211b44',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => '601a5ce9aaad7bf10797e3663faefce9e26c24e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => 'cf68d225bc43629de4ff54778029aee6dc191b83',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => '497f73ac996a598c92409b44ac43b6690c4f666d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '59eb412e93815df44f05f342958efa9f46b1e586',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => 'ec2344cf77a48253bbca6939aa3d2477773ea63d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/html-sanitizer' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => 'cf21254e982b12276329940ca4af5e623ee06c58',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/html-sanitizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => '4236baf01609667d53b20371486228231eb135fd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => 'ac7b8e163e8c83dce3abcc055a502d4486051a9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => '0f375bbbde96ae8c78e4aa3e63aabd486e33364c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => '0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '9614ac4d8061dc257ecc64cba1b140873dce8ad3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-uuid' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '21533be36c24be3f4b1669c4725c7d1d2bab4ae2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => '40c295f2deb408d5e9d2d32b8ba1dd61e36f05af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => '8e213820c5fea844ecea29203d2a308019007c15',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => 'f3570b8c61ca887a9e2938e85cb6458515d2b125',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => '4aba29076a29a3aa667e09b791e5f868973a8667',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'df210c7a2573f1913b2d17cc95f90f53a73d8f7d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/uid' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => '7beeb2b885cd584cd01e126c5777206ae4c3c6a3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/uid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => '548f6760c54197b1084e1e5c71f6d9d523f2f78e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '*******',
            'reference' => 'cea40a48279d58dc3efee8112634cb90141156c2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => '737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'tijsverkoyen/css-to-inline-styles' => array(
            'pretty_version' => 'v2.3.0',
            'version' => '2.3.0.0',
            'reference' => '0d72ac1c00084279c1816675284073c5a337c20d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tijsverkoyen/css-to-inline-styles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.2',
            'version' => '5.6.2.0',
            'reference' => '24ac4c74f91ee2c193fa1aaa5c249cb0822809af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'b1d923f88091c6bf09699efcd7c8a1b1bfd7351d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
