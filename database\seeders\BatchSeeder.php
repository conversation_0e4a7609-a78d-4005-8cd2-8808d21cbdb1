<?php

namespace Database\Seeders;

use App\Models\Drug;
use App\Models\Batch;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class BatchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $drugs = Drug::all();

        foreach ($drugs as $drug) {
            // Tạo 2-3 lô cho mỗi thuốc
            $batchCount = rand(2, 3);

            for ($i = 1; $i <= $batchCount; $i++) {
                $manufactureDate = Carbon::now()->subMonths(rand(1, 6));
                $expiryDate = $manufactureDate->copy()->addYears(rand(2, 5));
                $importDate = $manufactureDate->copy()->addDays(rand(1, 30));

                $importPrice = $drug->wholesale_price ?? rand(10000, 500000);
                $sellingPrice = $drug->price ?? ($importPrice * 1.3); // 30% markup

                Batch::create([
                    'drug_id' => $drug->id,
                    'batch_number' => 'LOT' . str_pad($drug->id, 3, '0', STR_PAD_LEFT) . str_pad($i, 2, '0', STR_PAD_LEFT),
                    'manufacture_date' => $manufactureDate,
                    'expiry_date' => $expiryDate,
                    'import_date' => $importDate,
                    'import_price' => $importPrice,
                    'selling_price' => $sellingPrice,
                    'initial_quantity' => rand(50, 500),
                    'current_quantity' => rand(10, 200),
                    'reserved_quantity' => 0,
                    'import_invoice_number' => 'IMP' . date('Ymd') . str_pad($drug->id * $i, 4, '0', STR_PAD_LEFT),
                    'notes' => 'Lô nhập tự động từ seeder',
                    'is_active' => true,
                ]);
            }
        }
    }
}
