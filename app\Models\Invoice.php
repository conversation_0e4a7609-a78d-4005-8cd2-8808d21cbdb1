<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class Invoice extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'invoice_number', 'customer_id', 'user_id', 'invoice_date',
        'subtotal', 'discount_amount', 'tax_amount', 'total_amount',
        'payment_method', 'payment_status', 'status', 'uuid',
        'tax_authority_status', 'tax_transaction_id', 'digital_signature',
        'notes', 'qr_code_data'
    ];

    protected $casts = [
        'invoice_date' => 'datetime',
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($invoice) {
            $invoice->uuid = Str::uuid();
            if (!$invoice->invoice_number) {
                $invoice->invoice_number = static::generateInvoiceNumber();
            }
        });
    }

    // Relationships
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    // Scopes
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('invoice_date', today());
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('invoice_date', now()->month)
            ->whereYear('invoice_date', now()->year);
    }

    public function scopeThisYear($query)
    {
        return $query->whereYear('invoice_date', now()->year);
    }

    // Accessors
    public function getCustomerPortalUrlAttribute(): string
    {
        return route('customer.invoice', $this->uuid);
    }

    public function getQrCodeUrlAttribute(): string
    {
        return "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" .
               urlencode($this->customer_portal_url);
    }

    public function getFormattedTotalAttribute(): string
    {
        return number_format($this->total_amount, 0, ',', '.') . ' ₫';
    }

    // Static Methods
    public static function generateInvoiceNumber(): string
    {
        $date = now()->format('Ymd');
        $sequence = static::whereDate('created_at', today())->count() + 1;

        return "HD{$date}" . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    // Business Methods
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum(function ($item) {
            return $item->quantity * $item->unit_price;
        });

        $this->tax_amount = $this->items->sum('tax_amount');
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->save();
    }

    public function confirm(): bool
    {
        if ($this->status !== 'draft') {
            return false;
        }

        DB::transaction(function () {
            // Update stock quantities using FEFO
            foreach ($this->items as $item) {
                $item->batch->sell($item->quantity);
            }

            // Update customer stats
            if ($this->customer) {
                $this->customer->updatePurchaseStats($this->total_amount);
                $this->customer->earnPoints($this->total_amount);
            }

            // Update invoice status
            $this->update(['status' => 'confirmed']);
        });

        return true;
    }

    public function cancel(): bool
    {
        if ($this->status !== 'confirmed') {
            return false;
        }

        DB::transaction(function () {
            // Restore stock quantities
            foreach ($this->items as $item) {
                $item->batch->increment('current_quantity', $item->quantity);
            }

            // Reverse customer stats
            if ($this->customer) {
                $this->customer->decrement('total_spent', $this->total_amount);
                // Reverse loyalty points (1 point per 1000 VND)
                $pointsToReverse = (int) floor($this->total_amount / 1000);
                $this->customer->decrement('loyalty_points', $pointsToReverse);
            }

            $this->update(['status' => 'cancelled']);
        });

        return true;
    }
}
