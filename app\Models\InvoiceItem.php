<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id', 'batch_id', 'quantity', 'unit_price',
        'discount_percentage', 'discount_amount', 'tax_rate',
        'tax_amount', 'total_price'
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->calculateAmounts();
        });
    }

    // Relationships
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function batch(): BelongsTo
    {
        return $this->belongsTo(Batch::class);
    }

    // Accessors
    public function getDrugNameAttribute(): string
    {
        return $this->batch->drug->name ?? '';
    }

    public function getFormattedTotalAttribute(): string
    {
        return number_format($this->total_price, 0, ',', '.') . ' ₫';
    }

    // Business Methods
    public function calculateAmounts(): void
    {
        // Calculate subtotal
        $subtotal = $this->quantity * $this->unit_price;

        // Calculate discount
        $this->discount_amount = $subtotal * ($this->discount_percentage / 100);

        // Calculate taxable amount
        $taxableAmount = $subtotal - $this->discount_amount;

        // Get tax rate from drug if not set
        if (!$this->tax_rate && $this->batch) {
            $this->tax_rate = $this->batch->drug->tax_rate * 100; // Convert to percentage
        }

        // Calculate tax
        $this->tax_amount = $taxableAmount * ($this->tax_rate / 100);

        // Calculate total
        $this->total_price = $taxableAmount + $this->tax_amount;
    }

    public function canFulfill(): bool
    {
        return $this->batch && $this->batch->canSell($this->quantity);
    }

    public function getSubtotalAttribute(): float
    {
        return $this->quantity * $this->unit_price;
    }

    public function getTaxableAmountAttribute(): float
    {
        return $this->subtotal - $this->discount_amount;
    }
}
