<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Drug;
use App\Models\Customer;

class PharmacyStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Tổng số thuốc', Drug::count())
                ->description('Số lượng thuốc trong kho')
                ->descriptionIcon('heroicon-m-beaker')
                ->color('success'),

            Stat::make('Thuốc đang hoạt động', Drug::active()->count())
                ->description('Thuốc có thể bán')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('primary'),

            Stat::make('Tổng khách hàng', Customer::count())
                ->description('Số lượng khách hàng')
                ->descriptionIcon('heroicon-m-users')
                ->color('warning'),
        ];
    }
}
